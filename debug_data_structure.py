#!/usr/bin/env python3
"""
调试数据结构 - 检查任务结果的实际格式
"""

import asyncio
import json
from web_service import TaskExecutor, AsyncTask

async def debug_task_result():
    print("🔍 调试任务结果数据结构...")
    
    # 创建一个测试任务
    task = AsyncTask(
        task_id="test_task",
        name="Test AI Query",
        task_type="ai_query",
        metadata={
            "question": "What tables do we have?",
            "options": {
                "includeRawData": True,
                "sessionId": "test_session"
            }
        }
    )
    
    print("📋 执行AI查询任务...")
    try:
        # 执行任务
        result_task = await TaskExecutor.execute_ai_query(task)
        
        print("✅ 任务执行完成")
        print("📊 任务状态:", result_task.status)
        print("📈 任务进度:", result_task.calculate_progress())
        
        if result_task.result:
            print("\n🎯 任务结果结构:")
            print("=" * 60)
            result_json = json.dumps(result_task.result, indent=2, default=str)
            print(result_json)
            print("=" * 60)
            
            # 检查数据字段
            result = result_task.result
            print(f"\n🔍 结果分析:")
            print(f"- 类型: {type(result)}")
            print(f"- 键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if 'data' in result:
                data = result['data']
                print(f"- data字段类型: {type(data)}")
                print(f"- data长度: {len(data) if data else 'None'}")
                if data and len(data) > 0:
                    print(f"- 第一行数据: {data[0]}")
                    print(f"- 第一行类型: {type(data[0])}")
                    if isinstance(data[0], dict):
                        print(f"- 第一行键: {list(data[0].keys())}")
            else:
                print("- ❌ 没有找到data字段")
                
            if 'sql' in result:
                print(f"- SQL: {result['sql']}")
            else:
                print("- ❌ 没有找到sql字段")
                
        else:
            print("❌ 任务没有返回结果")
            
    except Exception as e:
        print(f"❌ 任务执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_task_result())
