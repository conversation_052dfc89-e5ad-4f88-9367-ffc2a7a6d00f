#!/usr/bin/env python3
"""
Simple test for the web service API endpoints
"""

import requests
import json

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"Health check: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Status: {data['status']}")
            print(f"Database connected: {data['database_connected']}")
            print(f"Tables count: {data['tables_count']}")
            return True
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_database_info():
    """Test database info endpoint"""
    try:
        response = requests.get("http://localhost:8000/api/database-info", timeout=10)
        print(f"Database info: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Tables: {data['tables']}")
            print(f"Total records: {data['total_records']}")
            return True
        else:
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"Database info failed: {e}")
        return False

def test_query():
    """Test query endpoint"""
    try:
        query_data = {
            "question": "What tables do we have in the database?",
            "include_raw_data": False
        }
        response = requests.post(
            "http://localhost:8000/api/query", 
            json=query_data, 
            timeout=30
        )
        print(f"Query test: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Answer: {result['answer'][:200]}...")
            print(f"Execution time: {result['execution_time']:.2f}s")
            return True
        else:
            print(f"Query error: {response.text}")
            return False
    except Exception as e:
        print(f"Query test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Simple Web Service Test")
    print("=" * 40)
    
    # Test all endpoints
    health_ok = test_health()
    print()
    
    db_info_ok = test_database_info()
    print()
    
    query_ok = test_query()
    print()
    
    # Summary
    print("=" * 40)
    print("Test Results:")
    print(f"Health: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"Database Info: {'✅ PASS' if db_info_ok else '❌ FAIL'}")
    print(f"Query: {'✅ PASS' if query_ok else '❌ FAIL'}")
    
    if all([health_ok, db_info_ok, query_ok]):
        print("\n🎉 All tests passed! Web service is working correctly.")
        print("You can now open http://localhost:8000 in your browser.")
    else:
        print("\n❌ Some tests failed.")

if __name__ == "__main__":
    main()
