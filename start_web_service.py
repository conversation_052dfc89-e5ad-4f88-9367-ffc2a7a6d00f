#!/usr/bin/env python3
"""
Startup script for AutoGen + ClickHouse Web Service
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import fastapi
        import uvicorn
        import autogen_agentchat
        import autogen_ext
        print("✅ All web service dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("Installing required packages...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "fastapi>=0.104.0", 
                "uvicorn[standard]>=0.24.0",
                "websockets>=12.0",
                "python-multipart>=0.0.6"
            ], check=True)
            print("✅ Web service dependencies installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False

def check_env_file():
    """Check if .env file exists and is configured."""
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found")
        if Path('.env.example').exists():
            print("📝 Copying .env.example to .env")
            import shutil
            shutil.copy('.env.example', '.env')
            print("⚠️  Please edit .env file with your configuration")
            return False
        else:
            print("❌ No .env.example found either")
            return False
    
    # Check if required variables are set
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ['OPENAI_API_KEY', 'LLM_PROVIDER']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        return False
    
    print("✅ Environment configuration is valid")
    return True

def check_database():
    """Check if database is accessible."""
    try:
        from clickhouse_client import clickhouse_client
        tables = clickhouse_client.get_tables()
        print(f"✅ Database connected. Found {len(tables)} tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Try running: python setup_database.py")
        return False

def start_web_service():
    """Start the web service."""
    print("🚀 Starting AutoGen + ClickHouse Web Service...")
    print("📱 Web interface will be available at: http://localhost:8000")
    print("📊 API documentation at: http://localhost:8000/docs")
    print("🔍 Health check at: http://localhost:8000/health")
    print("\nPress Ctrl+C to stop the service")
    print("-" * 60)
    
    try:
        import uvicorn
        uvicorn.run(
            "web_service:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Web service stopped")
    except Exception as e:
        print(f"❌ Error starting web service: {e}")

def main():
    """Main startup function."""
    print("🌐 AutoGen + ClickHouse Web Service Startup")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("Please install dependencies and try again")
        return 1
    
    # Check environment
    if not check_env_file():
        print("Please configure your .env file and try again")
        return 1
    
    # Check database
    if not check_database():
        print("Please ensure ClickHouse is running and database is set up")
        return 1
    
    # Start service
    start_web_service()
    return 0

if __name__ == "__main__":
    sys.exit(main())
