#!/usr/bin/env python3
"""
Simple test to check basic functionality
"""

import asyncio
from playwright.async_api import async_playwright

async def simple_test():
    """Simple test of the web interface."""
    
    async with async_playwright() as p:
        print("🧪 Simple test...")
        
        # Launch browser
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # Enable console logging
        page.on("console", lambda msg: print(f"Console: {msg.text}"))
        page.on("pageerror", lambda error: print(f"Error: {error}"))
        
        try:
            # Navigate to the page
            print("📱 Loading page...")
            await page.goto("http://localhost:8000")
            
            # Wait for page to load
            await page.wait_for_selector("h1", timeout=10000)
            print("✅ Page loaded")
            
            # Wait for initialization
            await page.wait_for_timeout(5000)
            
            # Check basic elements
            title = await page.text_content("h1")
            print(f"Title: {title}")
            
            # Check if input exists
            input_exists = await page.query_selector("#question-input") is not None
            print(f"Input exists: {input_exists}")
            
            # Check if button exists
            button_exists = await page.query_selector("#ask-button") is not None
            print(f"Button exists: {button_exists}")
            
            # Check jQuery
            jquery_loaded = await page.evaluate("() => typeof $ !== 'undefined'")
            print(f"jQuery loaded: {jquery_loaded}")
            
            # Check if our functions exist
            submit_query_exists = await page.evaluate("() => typeof window.submitQuery === 'function'")
            print(f"submitQuery function exists: {submit_query_exists}")
            
            # Try to call database info loading
            db_info_result = await page.evaluate("() => typeof loadDatabaseInfo === 'function'")
            print(f"loadDatabaseInfo function exists: {db_info_result}")
            
            # Manual test - fill input and click button
            print("🖱️ Testing manual interaction...")
            await page.fill("#question-input", "test question")
            
            input_value = await page.input_value("#question-input")
            print(f"Input value: '{input_value}'")
            
            # Try clicking button
            await page.click("#ask-button")
            print("✅ Button clicked")
            
            # Wait and see what happens
            await page.wait_for_timeout(5000)
            
            # Keep browser open for manual inspection
            print("⏳ Keeping browser open for 15 seconds for manual inspection...")
            await page.wait_for_timeout(15000)
            
        except Exception as e:
            print(f"❌ Test error: {e}")
            
        finally:
            await browser.close()
            print("🎭 Browser closed")

if __name__ == "__main__":
    asyncio.run(simple_test())
