# 前后端分离架构说明

## 🏗️ 架构概述

本项目已重构为前后端分离架构，提供更好的代码组织和维护性。

### 后端 (FastAPI)
- **文件**: `web_service.py`
- **职责**: 提供RESTful API接口
- **端口**: 8000
- **功能**:
  - 数据库连接和查询
  - AI模型集成
  - SQL执行和验证
  - 数据导出

### 前端 (HTML/CSS/JavaScript)
- **目录**: `static/`
- **技术栈**: 原生JavaScript + Tailwind CSS
- **文件结构**:
  ```
  static/
  ├── css/
  │   └── style.css      # 自定义样式
  ├── js/
  │   ├── app.js         # 主应用逻辑
  │   └── utils.js       # 工具函数
  ```

## 🚀 新功能特性

### 1. 改进的SQL生成
- ✅ 动态字段信息获取
- ✅ 智能字段含义推理
- ✅ 中文字段描述支持
- ✅ 字段映射提醒

### 2. 直接SQL执行
- ✅ 专用SQL执行界面
- ✅ 语法安全验证
- ✅ 实时结果显示
- ✅ 快捷键支持 (Ctrl+Enter)

### 3. 数据导出功能
- ✅ CSV格式导出
- ✅ JSON格式导出
- ✅ 一键下载
- ✅ 自动文件命名

### 4. SQL透明度
- ✅ 显示执行的SQL语句
- ✅ 格式化代码显示
- ✅ 执行时间统计
- ✅ 中文提示信息

## 📁 文件说明

### 后端文件
- `web_service.py` - 主服务文件，包含所有API端点
- `clickhouse_client.py` - 数据库客户端
- `config.py` - 配置管理

### 前端文件
- `static/css/style.css` - 自定义CSS样式
- `static/js/app.js` - 主应用类和核心逻辑
- `static/js/utils.js` - 工具函数和UI操作

## 🔌 API端点

### 核心API
- `GET /` - 主页面
- `GET /api/database-info` - 获取数据库信息
- `POST /api/query` - AI查询处理
- `POST /api/execute-sql` - 直接SQL执行
- `POST /api/export-query-result` - 查询结果导出
- `GET /health` - 健康检查

### WebSocket
- `WS /ws` - 实时通信 (预留)

## 🎨 前端架构

### 主应用类 (DataAnalysisApp)
```javascript
class DataAnalysisApp {
    constructor()     // 初始化
    init()           // 应用启动
    renderUI()       // 渲染界面
    setupEventHandlers() // 事件绑定
    loadDatabaseInfo()   // 加载数据库信息
    submitQuery()        // 提交AI查询
    executeSQLQuery()    // 执行SQL查询
    displayChart()       // 显示图表
}
```

### 工具函数 (utils.js)
- UI状态管理 (`setLoading`, `setSQLLoading`)
- 错误处理 (`showError`, `hideError`)
- 结果显示 (`displayResults`, `displaySQLResults`)
- 数据导出 (`exportQueryData`, `downloadFile`)
- 文本格式化 (`formatAnswer`)

## 🔧 开发指南

### 启动服务
```bash
python web_service.py
```

### 访问应用
```
http://localhost:8000
```

### 开发模式
- 后端支持热重载
- 前端文件修改后刷新浏览器即可

### 添加新功能
1. **后端**: 在 `web_service.py` 中添加新的API端点
2. **前端**: 在 `app.js` 中添加新的方法，在 `utils.js` 中添加工具函数

## 🎯 优势

### 代码组织
- ✅ 前后端职责分离
- ✅ 模块化JavaScript代码
- ✅ 可复用的工具函数
- ✅ 清晰的文件结构

### 开发体验
- ✅ 更好的代码可读性
- ✅ 更容易调试和维护
- ✅ 支持独立开发和测试
- ✅ 更好的错误处理

### 用户体验
- ✅ 响应式设计
- ✅ 流畅的交互
- ✅ 清晰的视觉反馈
- ✅ 快捷键支持

## 🔮 未来扩展

### 可能的改进
- [ ] TypeScript支持
- [ ] 前端框架集成 (React/Vue)
- [ ] 更丰富的图表库
- [ ] 实时数据更新
- [ ] 用户认证系统
- [ ] 多语言支持

### 性能优化
- [ ] 前端资源压缩
- [ ] API响应缓存
- [ ] 懒加载实现
- [ ] CDN集成

这个新架构为项目提供了更好的可扩展性和维护性，同时保持了所有原有功能的完整性。
