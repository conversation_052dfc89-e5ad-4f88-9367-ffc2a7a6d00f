#!/usr/bin/env python3
"""
Test script for AutoGen + ClickHouse demo.
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock
import pandas as pd

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from clickhouse_client import C<PERSON><PERSON>ouse<PERSON>lient
from agents import execute_clickhouse_query, get_database_schema, get_table_sample
from config import CLICKHOUSE_CONFIG, SAMPLE_TABLES

class TestClickHouseClient(unittest.TestCase):
    """Test ClickHouse client functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client = ClickHouseClient()
    
    @patch('clickhouse_connect.get_client')
    def test_connection(self, mock_get_client):
        """Test database connection."""
        mock_client = MagicMock()
        mock_get_client.return_value = mock_client
        
        client = ClickHouseClient()
        self.assertIsNotNone(client.client)
        mock_get_client.assert_called_once()
    
    def test_config_loading(self):
        """Test configuration loading."""
        self.assertIsInstance(CLICKHOUSE_CONFIG, dict)
        self.assertIn('host', CLICKHOUSE_CONFIG)
        self.assertIn('port', CLICKHOUSE_CONFIG)
        self.assertIn('database', CLICKHOUSE_CONFIG)

class TestAgentFunctions(unittest.TestCase):
    """Test agent helper functions."""
    
    @patch('clickhouse_client.clickhouse_client')
    def test_execute_query_security(self, mock_client):
        """Test query security validation."""
        # Test valid SELECT query
        mock_client.execute_query.return_value = pd.DataFrame({'col1': [1, 2, 3]})
        result = execute_clickhouse_query("SELECT * FROM test")
        self.assertIn("Query returned", result)
        
        # Test invalid query (non-SELECT)
        result = execute_clickhouse_query("DROP TABLE test")
        self.assertIn("Error: Only SELECT queries", result)
        
        # Test invalid query (INSERT)
        result = execute_clickhouse_query("INSERT INTO test VALUES (1)")
        self.assertIn("Error: Only SELECT queries", result)
    
    @patch('clickhouse_client.clickhouse_client')
    def test_get_schema(self, mock_client):
        """Test schema retrieval."""
        mock_client.get_tables.return_value = ['test_table']
        mock_client.get_table_schema.return_value = [
            {'name': 'id', 'type': 'UInt32'},
            {'name': 'name', 'type': 'String'}
        ]
        
        schema = get_database_schema()
        self.assertIn("Database Schema", schema)
        self.assertIn("test_table", schema)
        self.assertIn("id: UInt32", schema)
    
    @patch('clickhouse_client.clickhouse_client')
    def test_get_table_sample(self, mock_client):
        """Test table sample retrieval."""
        mock_client.get_table_sample.return_value = pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['A', 'B', 'C']
        })
        
        sample = get_table_sample('test_table', 3)
        self.assertIn("Sample data from test_table", sample)
        self.assertIn("id", sample)
        self.assertIn("name", sample)

class TestConfiguration(unittest.TestCase):
    """Test configuration and setup."""
    
    def test_sample_tables_config(self):
        """Test sample tables configuration."""
        self.assertIsInstance(SAMPLE_TABLES, dict)
        
        for table_name, config in SAMPLE_TABLES.items():
            self.assertIn('columns', config)
            self.assertIn('engine', config)
            self.assertIn('order_by', config)
            self.assertIsInstance(config['columns'], list)
            self.assertTrue(len(config['columns']) > 0)
    
    def test_required_tables(self):
        """Test that required tables are defined."""
        required_tables = ['sales', 'products', 'customers']
        for table in required_tables:
            self.assertIn(table, SAMPLE_TABLES)

class TestIntegration(unittest.TestCase):
    """Integration tests (require actual database)."""
    
    def setUp(self):
        """Set up integration test."""
        try:
            from clickhouse_client import clickhouse_client
            self.client = clickhouse_client
            # Test connection
            self.client.get_tables()
            self.db_available = True
        except Exception:
            self.db_available = False
    
    def test_database_connection(self):
        """Test actual database connection."""
        if not self.db_available:
            self.skipTest("Database not available")
        
        tables = self.client.get_tables()
        self.assertIsInstance(tables, list)
    
    def test_sample_data_queries(self):
        """Test sample data queries."""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Test basic queries
        test_queries = [
            "SELECT COUNT(*) as count FROM sales",
            "SELECT COUNT(*) as count FROM products", 
            "SELECT COUNT(*) as count FROM customers"
        ]
        
        for query in test_queries:
            try:
                result = self.client.execute_query(query)
                self.assertIsInstance(result, pd.DataFrame)
                self.assertTrue(len(result) > 0)
            except Exception as e:
                self.fail(f"Query failed: {query}, Error: {e}")

def run_basic_tests():
    """Run basic tests that don't require database."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add tests that don't require database
    suite.addTests(loader.loadTestsFromTestCase(TestConfiguration))
    suite.addTests(loader.loadTestsFromTestCase(TestAgentFunctions))
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

def run_integration_tests():
    """Run integration tests that require database."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add integration tests
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    return result.wasSuccessful()

def main():
    """Main test runner."""
    print("AutoGen + ClickHouse Demo Tests")
    print("=" * 40)
    
    # Run basic tests
    print("\n1. Running basic tests...")
    basic_success = run_basic_tests()
    
    # Run integration tests
    print("\n2. Running integration tests...")
    integration_success = run_integration_tests()
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Summary:")
    print(f"Basic tests: {'PASSED' if basic_success else 'FAILED'}")
    print(f"Integration tests: {'PASSED' if integration_success else 'FAILED'}")
    
    if basic_success and integration_success:
        print("\nAll tests passed! ✅")
        return 0
    else:
        print("\nSome tests failed! ❌")
        return 1

if __name__ == "__main__":
    sys.exit(main())
