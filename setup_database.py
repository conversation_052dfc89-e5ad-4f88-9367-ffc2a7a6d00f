#!/usr/bin/env python3
"""
Setup script to initialize ClickHouse database with sample data.
"""

import random
from datetime import datetime, timedelta
from clickhouse_client import clickhouse_client
from config import SAMPLE_TABLES
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_database():
    """Create the demo database if it doesn't exist."""
    try:
        clickhouse_client.execute_command(f"CREATE DATABASE IF NOT EXISTS {clickhouse_client.config['database']}")
        logger.info(f"Database {clickhouse_client.config['database']} created/verified")
    except Exception as e:
        logger.error(f"Failed to create database: {e}")
        raise

def create_tables():
    """Create sample tables."""
    for table_name, table_config in SAMPLE_TABLES.items():
        columns_def = ", ".join(table_config["columns"])
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {columns_def}
        ) ENGINE = {table_config["engine"]}
        ORDER BY {table_config["order_by"]}
        """
        
        try:
            clickhouse_client.execute_command(create_sql)
            logger.info(f"Table {table_name} created successfully")
        except Exception as e:
            logger.error(f"Failed to create table {table_name}: {e}")
            raise

def generate_sample_data():
    """Generate and insert sample data."""
    
    # Generate products
    products_data = []
    categories = ["Electronics", "Clothing", "Books", "Home & Garden", "Sports"]
    for i in range(1, 101):
        products_data.append({
            "id": i,
            "name": f"Product {i}",
            "category": random.choice(categories),
            "price": round(random.uniform(10, 1000), 2),
            "created_date": (datetime.now() - timedelta(days=random.randint(1, 365))).strftime('%Y-%m-%d')
        })
    
    # Insert products
    for product in products_data:
        insert_sql = f"""
        INSERT INTO products VALUES (
            {product['id']}, 
            '{product['name']}', 
            '{product['category']}', 
            {product['price']}, 
            '{product['created_date']}'
        )
        """
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(products_data)} products")
    
    # Generate customers
    customers_data = []
    countries = ["USA", "Canada", "UK", "Germany", "France", "Japan", "Australia"]
    for i in range(1, 201):
        customers_data.append({
            "id": i,
            "name": f"Customer {i}",
            "email": f"customer{i}@example.com",
            "registration_date": (datetime.now() - timedelta(days=random.randint(1, 730))).strftime('%Y-%m-%d'),
            "country": random.choice(countries),
            "age": random.randint(18, 80)
        })
    
    # Insert customers
    for customer in customers_data:
        insert_sql = f"""
        INSERT INTO customers VALUES (
            {customer['id']}, 
            '{customer['name']}', 
            '{customer['email']}', 
            '{customer['registration_date']}', 
            '{customer['country']}', 
            {customer['age']}
        )
        """
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(customers_data)} customers")
    
    # Generate sales
    regions = ["North", "South", "East", "West", "Central"]
    sales_data = []
    for i in range(1, 1001):
        product_id = random.randint(1, 100)
        customer_id = random.randint(1, 200)
        quantity = random.randint(1, 10)
        unit_price = round(random.uniform(10, 1000), 2)
        total_amount = round(quantity * unit_price, 2)
        
        sales_data.append({
            "id": i,
            "product_id": product_id,
            "customer_id": customer_id,
            "sale_date": (datetime.now() - timedelta(days=random.randint(1, 365))).strftime('%Y-%m-%d'),
            "quantity": quantity,
            "unit_price": unit_price,
            "total_amount": total_amount,
            "region": random.choice(regions)
        })
    
    # Insert sales
    for sale in sales_data:
        insert_sql = f"""
        INSERT INTO sales VALUES (
            {sale['id']}, 
            {sale['product_id']}, 
            {sale['customer_id']}, 
            '{sale['sale_date']}', 
            {sale['quantity']}, 
            {sale['unit_price']}, 
            {sale['total_amount']}, 
            '{sale['region']}'
        )
        """
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(sales_data)} sales records")

def main():
    """Main setup function."""
    logger.info("Starting database setup...")
    
    try:
        create_database()
        create_tables()
        generate_sample_data()
        
        # Verify setup
        tables = clickhouse_client.get_tables()
        logger.info(f"Setup complete. Created tables: {tables}")
        
        # Show sample data
        for table in tables:
            sample = clickhouse_client.get_table_sample(table, 3)
            logger.info(f"Sample data from {table}:")
            print(sample)
            print()
            
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        raise
    finally:
        clickhouse_client.close()

if __name__ == "__main__":
    main()
