<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Data Display</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-white mb-8">Debug Data Display</h1>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 mb-6">
            <h2 class="text-xl font-semibold text-white mb-4">Test createDataTable Function</h2>
            
            <button id="test-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg mb-4">
                Test Data Table
            </button>
            
            <div id="result-container" class="bg-white rounded-xl p-4 mt-4">
                <!-- Result will appear here -->
            </div>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h2 class="text-xl font-semibold text-white mb-4">Console Output</h2>
            <div id="console-output" class="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                <!-- Console output will appear here -->
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script>
        // Capture console.log output
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, arguments);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // Test data - exactly like what the backend returns
        const testData = [
            { "name": "customers" },
            { "name": "sales" },
            { "name": "products" },
            { "name": "traffic_logs_with_geo" }
        ];

        document.getElementById('test-btn').addEventListener('click', () => {
            console.log('🧪 Starting test...');
            console.log('Test data:', testData);
            
            // Test the createDataTable function
            const tableHtml = createDataTable(testData);
            console.log('Generated HTML:', tableHtml);
            
            // Display the result
            document.getElementById('result-container').innerHTML = tableHtml;
            
            console.log('✅ Test completed');
        });

        console.log('🚀 Debug page loaded');
        console.log('Available functions:', typeof createDataTable);
    </script>
</body>
</html>
