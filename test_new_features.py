#!/usr/bin/env python3
"""
测试新功能：prompt优化和任务拆解
"""

import requests
import json
import time

def test_prompt_optimization():
    """测试prompt优化功能"""
    
    print("🧪 测试Prompt优化功能")
    print("=" * 50)
    
    url = "http://localhost:8000/api/query"
    
    # 测试一个模糊的问题
    test_cases = [
        {
            "question": "分析数据",
            "description": "模糊问题"
        },
        {
            "question": "销售情况",
            "description": "简单问题"
        },
        {
            "question": "给我一些见解",
            "description": "非常模糊的问题"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {test_case['description']}")
        print(f"原始问题: '{test_case['question']}'")
        
        # 不优化的请求
        payload_no_opt = {
            "question": test_case["question"],
            "include_raw_data": False,
            "session_id": f"test_opt_{i}_no",
            "optimize_prompt": False,
            "enable_task_decomposition": False
        }
        
        # 优化的请求
        payload_opt = {
            "question": test_case["question"],
            "include_raw_data": True,
            "session_id": f"test_opt_{i}_yes",
            "optimize_prompt": True,
            "enable_task_decomposition": False
        }
        
        try:
            print("发送优化请求...")
            response_opt = requests.post(url, json=payload_opt, timeout=120)
            
            if response_opt.status_code == 200:
                result_opt = response_opt.json()
                
                # 检查是否有优化信息
                if result_opt.get('raw_data') and 'processed_question' in result_opt['raw_data']:
                    processed_q = result_opt['raw_data']['processed_question']
                    original_q = result_opt['raw_data']['original_question']
                    
                    if processed_q != original_q:
                        print(f"✅ 优化成功!")
                        print(f"优化后问题: '{processed_q}'")
                        print(f"执行时间: {result_opt['execution_time']:.2f}s")
                    else:
                        print("⚠️  问题未被优化")
                else:
                    print("⚠️  无法获取优化信息")
            else:
                print(f"❌ 请求失败: {response_opt.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)

def test_task_decomposition():
    """测试任务拆解功能"""
    
    print("\n🧪 测试任务拆解功能")
    print("=" * 50)
    
    url = "http://localhost:8000/api/query"
    
    # 测试复杂问题
    complex_questions = [
        "创建一个全面的商业智能报告，包括销售分析、客户分析和产品分析",
        "比较所有维度的业务表现并提供战略建议",
        "分析我们的业务数据并找出增长机会和风险点"
    ]
    
    for i, question in enumerate(complex_questions, 1):
        print(f"\n测试案例 {i}:")
        print(f"复杂问题: '{question}'")
        
        payload = {
            "question": question,
            "include_raw_data": True,
            "session_id": f"test_decomp_{i}",
            "optimize_prompt": False,
            "enable_task_decomposition": True
        }
        
        try:
            print("发送任务拆解请求...")
            response = requests.post(url, json=payload, timeout=180)
            
            if response.status_code == 200:
                result = response.json()
                
                # 检查是否有拆解信息
                if result.get('raw_data') and 'sub_tasks' in result['raw_data']:
                    sub_tasks = result['raw_data']['sub_tasks']
                    
                    if sub_tasks and len(sub_tasks) > 1:
                        print(f"✅ 任务拆解成功! 拆解为 {len(sub_tasks)} 个子任务:")
                        for j, task in enumerate(sub_tasks, 1):
                            print(f"  {j}. {task}")
                        print(f"执行时间: {result['execution_time']:.2f}s")
                        
                        # 检查答案是否包含子任务结果
                        answer = result['answer']
                        if "Sub-task" in answer:
                            print("✅ 答案包含子任务执行结果")
                        else:
                            print("⚠️  答案可能未包含所有子任务结果")
                    else:
                        print("⚠️  任务未被拆解或只有一个子任务")
                else:
                    print("⚠️  无法获取拆解信息")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 30)

def test_combined_features():
    """测试组合功能"""
    
    print("\n🧪 测试组合功能 (优化 + 拆解)")
    print("=" * 50)
    
    url = "http://localhost:8000/api/query"
    
    question = "分析业务表现"
    
    payload = {
        "question": question,
        "include_raw_data": True,
        "session_id": "test_combined",
        "optimize_prompt": True,
        "enable_task_decomposition": True
    }
    
    try:
        print(f"原始问题: '{question}'")
        print("发送组合功能请求...")
        
        response = requests.post(url, json=payload, timeout=240)
        
        if response.status_code == 200:
            result = response.json()
            raw_data = result.get('raw_data', {})
            
            # 检查优化
            if raw_data.get('processed_question') != raw_data.get('original_question'):
                print(f"✅ Prompt优化: '{raw_data.get('processed_question')}'")
            
            # 检查拆解
            sub_tasks = raw_data.get('sub_tasks', [])
            if sub_tasks and len(sub_tasks) > 1:
                print(f"✅ 任务拆解: {len(sub_tasks)} 个子任务")
            
            print(f"总执行时间: {result['execution_time']:.2f}s")
            print("✅ 组合功能测试完成")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    print("🚀 测试新功能：Prompt优化和任务拆解")
    print("=" * 70)
    
    # 运行测试
    test_prompt_optimization()
    test_task_decomposition()
    test_combined_features()
    
    print("\n" + "=" * 70)
    print("📊 测试完成")
    print("=" * 70)
