#!/usr/bin/env python3
"""
Final comprehensive test for the web service
"""

import asyncio
import requests
from playwright.async_api import async_playwright

async def test_complete_workflow():
    """Test the complete workflow from frontend to backend"""
    
    print("🧪 Final Web Service Test")
    print("=" * 50)
    
    # 1. Test backend API directly
    print("1. Testing backend API...")
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={"question": "What tables do we have?", "include_raw_data": False},
            timeout=30
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Backend API works: {result['answer'][:100]}...")
        else:
            print(f"❌ Backend API failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend API error: {e}")
        return False
    
    # 2. Test frontend with Playwright
    print("\n2. Testing frontend interface...")
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # Navigate to page
            await page.goto("http://localhost:8000")
            await page.wait_for_selector("h1", timeout=10000)
            print("✅ Page loaded successfully")
            
            # Fill in question
            await page.fill("input[placeholder*='Ask a question']", "What tables do we have?")
            print("✅ Question entered")
            
            # Click Ask button
            await page.click("button:has-text('Ask')")
            print("✅ Ask button clicked")
            
            # Wait for results
            await page.wait_for_timeout(15000)  # Wait 15 seconds for AI response
            
            # Check for results
            results = await page.query_selector_all(".bg-white.rounded-lg.shadow-md")
            print(f"✅ Found {len(results)} result containers")
            
            if len(results) >= 2:  # Header + at least one result
                print("✅ Query result appears to be displayed")
                
                # Try to get the actual response text
                try:
                    # Look for any text content that might be the response
                    page_content = await page.content()
                    if "customers" in page_content and "products" in page_content and "sales" in page_content:
                        print("✅ Response contains expected table names")
                    else:
                        print("⚠️  Response content unclear")
                except:
                    print("⚠️  Could not verify response content")
                
                return True
            else:
                print("❌ No query results found")
                return False
                
        except Exception as e:
            print(f"❌ Frontend test error: {e}")
            return False
        finally:
            await browser.close()

async def main():
    """Main test function"""
    success = await test_complete_workflow()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: Web service is working correctly!")
        print("✅ Backend API responds to queries")
        print("✅ Frontend can submit queries")
        print("✅ Results are displayed")
        print("\n🌐 You can now use the web interface at: http://localhost:8000")
    else:
        print("❌ FAILURE: Some issues were found")
        print("Please check the service logs for more details")

if __name__ == "__main__":
    asyncio.run(main())
