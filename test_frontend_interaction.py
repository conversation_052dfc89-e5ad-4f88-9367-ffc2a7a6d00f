#!/usr/bin/env python3
"""
Test frontend interaction with detailed debugging
"""

import asyncio
import time
from playwright.async_api import async_playwright

async def test_frontend_interaction():
    """Test frontend interaction step by step"""
    
    print("🧪 Testing Frontend Interaction")
    print("=" * 50)
    
    async with async_playwright() as p:
        # Launch browser in non-headless mode to see what's happening
        browser = await p.chromium.launch(headless=False, slow_mo=1000)  # Slow down for visibility
        page = await browser.new_page()
        
        try:
            print("1. 📱 Navigating to web service...")
            await page.goto("http://localhost:8000")
            
            print("2. ⏳ Waiting for page to load...")
            await page.wait_for_selector("h1", timeout=10000)
            
            title = await page.text_content("h1")
            print(f"✅ Page loaded: {title.strip()}")
            
            print("3. 🔍 Looking for input field...")
            input_selector = "input[placeholder*='Ask a question']"
            await page.wait_for_selector(input_selector, timeout=5000)
            print("✅ Input field found")
            
            print("4. ✏️ Typing question...")
            test_question = "What tables do we have in the database?"
            await page.fill(input_selector, test_question)
            
            # Verify the text was entered
            input_value = await page.input_value(input_selector)
            print(f"✅ Question entered: '{input_value}'")
            
            print("5. 🔍 Looking for Ask button...")
            button_selector = "button:has-text('Ask')"
            await page.wait_for_selector(button_selector, timeout=5000)
            
            # Check if button is enabled
            is_disabled = await page.is_disabled(button_selector)
            print(f"Button disabled: {is_disabled}")
            
            if is_disabled:
                print("❌ Button is disabled, checking why...")
                # Check Alpine.js state
                loading_state = await page.evaluate("() => window.Alpine && window.Alpine.store ? 'Alpine loaded' : 'Alpine not loaded'")
                print(f"Alpine.js state: {loading_state}")
            else:
                print("✅ Button is enabled")
            
            print("6. 🖱️ Clicking Ask button...")
            await page.click(button_selector, force=True)  # Force click even if disabled
            print("✅ Button clicked")
            
            print("7. ⏳ Waiting for loading state...")
            # Wait a moment to see loading state
            await page.wait_for_timeout(2000)
            
            # Check for loading indicator
            loading_visible = await page.is_visible("text=AI is analyzing")
            print(f"Loading indicator visible: {loading_visible}")
            
            print("8. ⏳ Waiting for response (30 seconds)...")
            # Wait for response
            await page.wait_for_timeout(30000)
            
            print("9. 🔍 Checking for results...")
            # Look for result containers
            result_containers = await page.query_selector_all(".bg-white.rounded-lg.shadow-md")
            print(f"Found {len(result_containers)} result containers")
            
            # Look for specific result content
            page_content = await page.content()
            
            if "customers" in page_content or "products" in page_content or "sales" in page_content:
                print("✅ Response contains expected table names")
            else:
                print("⚠️  Response content not found")
            
            # Look for error messages
            error_visible = await page.is_visible("text=Error")
            if error_visible:
                error_text = await page.text_content(".text-red-700")
                print(f"❌ Error found: {error_text}")
            else:
                print("✅ No errors visible")
            
            # Check console logs
            print("10. 📋 Checking console logs...")
            
            # Take a screenshot
            await page.screenshot(path="frontend_test.png", full_page=True)
            print("📸 Screenshot saved as frontend_test.png")
            
            print("11. ⏳ Keeping browser open for 10 seconds for manual inspection...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"❌ Test error: {e}")
            await page.screenshot(path="frontend_error.png", full_page=True)
            print("📸 Error screenshot saved")
        
        finally:
            await browser.close()
            print("🎭 Browser closed")

async def main():
    """Main test function"""
    await test_frontend_interaction()
    
    print("\n" + "=" * 50)
    print("🏁 Frontend interaction test completed")
    print("Check the screenshots for visual confirmation")

if __name__ == "__main__":
    asyncio.run(main())
