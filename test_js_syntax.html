<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JS Syntax</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test Page</h1>
    <input type="text" id="question-input" placeholder="Ask a question">
    <button id="ask-button">Ask</button>
    <div id="results"></div>

    <script>
        // Global variables
        let loading = false;
        let results = [];
        
        // Initialize when document is ready
        $(document).ready(function() {
            console.log('Document ready, initializing...');
            setupEventHandlers();
        });
        
        // Setup event handlers
        function setupEventHandlers() {
            // Ask button click
            $('#ask-button').click(function() {
                submitQuery();
            });
            
            // Enter key in input
            $('#question-input').keypress(function(e) {
                if (e.which === 13) { // Enter key
                    submitQuery();
                }
            });
        }
        
        // Submit query function (global)
        window.submitQuery = async function() {
            console.log('submitQuery called');
            
            const question = $('#question-input').val().trim();
            if (!question) {
                alert('Please enter a question');
                return;
            }
            
            console.log('Question:', question);
            $('#results').html('<p>Processing: ' + question + '</p>');
        };
        
        // Test function
        window.testFunction = function() {
            console.log('Test function called');
            return 'Test successful';
        };
    </script>
</body>
</html>
