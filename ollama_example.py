#!/usr/bin/env python3
"""
Ollama-specific example for AutoGen + ClickHouse demo.
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_ollama_connection():
    """Check if Ollama server is accessible."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama server is running with {len(models)} models")
            for model in models:
                print(f"  - {model['name']}")
            return len(models) > 0
        else:
            print("❌ Ollama server responded with error")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Ollama server: {e}")
        print("Make sure Ollama is running: ollama serve")
        return False

def test_ollama_model(model_name):
    """Test Ollama model with a simple prompt."""
    print(f"\n🧪 Testing model: {model_name}")
    
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": model_name,
                "prompt": "What is ClickHouse? Please provide a brief explanation.",
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '').strip()
            print(f"✅ Model response:\n{response_text}")
            return True
        else:
            print(f"❌ Model test failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def run_ollama_agent_demo():
    """Run a simple agent demo with Ollama."""
    print("\n🤖 Running Ollama Agent Demo")
    print("=" * 40)
    
    # Set environment for Ollama
    os.environ['LLM_PROVIDER'] = 'ollama'
    
    try:
        from clickhouse_client import clickhouse_client
        from agents import AgentFactory, execute_clickhouse_query
        import autogen
        
        # Check database connection
        tables = clickhouse_client.get_tables()
        if not tables:
            print("❌ No database tables found. Run setup_database.py first.")
            return False
        
        print(f"✅ Database connected with tables: {tables}")
        
        # Create agents
        agents = AgentFactory.create_agents()
        
        # Override user proxy for automated demo
        agents["user_proxy"] = autogen.UserProxyAgent(
            name="User",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=3,
            is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
            code_execution_config=False,
        )
        
        # Create group chat
        group_chat = AgentFactory.create_group_chat(agents)
        manager = AgentFactory.create_group_chat_manager(group_chat)
        
        print("\n🚀 Starting agent conversation...")
        
        # Test query
        agents["user_proxy"].initiate_chat(
            manager,
            message="Show me the total sales by region from the database. TERMINATE when done."
        )
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

def interactive_ollama_chat():
    """Interactive chat with Ollama model."""
    print("\n💬 Interactive Ollama Chat")
    print("=" * 40)
    print("Type 'quit' to exit")
    
    # Get available models
    try:
        response = requests.get("http://localhost:11434/api/tags")
        models = response.json().get('models', [])
        
        if not models:
            print("❌ No models available. Pull a model first: ollama pull llama2:7b")
            return
        
        # Use first available model
        model_name = models[0]['name']
        print(f"Using model: {model_name}")
        
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return
    
    while True:
        try:
            user_input = input("\nYou: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            print("🤖 Thinking...")
            
            response = requests.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": model_name,
                    "prompt": user_input,
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '').strip()
                print(f"Assistant: {response_text}")
            else:
                print(f"❌ Error: {response.text}")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main function."""
    print("🦙 Ollama + AutoGen + ClickHouse Demo")
    print("=" * 50)
    
    # Check Ollama connection
    if not check_ollama_connection():
        print("\n💡 To get started with Ollama:")
        print("1. Install Ollama: https://ollama.ai/download")
        print("2. Start server: ollama serve")
        print("3. Pull a model: ollama pull llama2:7b")
        print("4. Or use Docker: make start-ollama")
        return 1
    
    # Test a model
    try:
        response = requests.get("http://localhost:11434/api/tags")
        models = response.json().get('models', [])
        if models:
            test_ollama_model(models[0]['name'])
    except:
        pass
    
    # Show menu
    while True:
        print("\nSelect an option:")
        print("1. Run Agent Demo")
        print("2. Interactive Chat")
        print("3. Test Model")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            run_ollama_agent_demo()
        elif choice == "2":
            interactive_ollama_chat()
        elif choice == "3":
            try:
                response = requests.get("http://localhost:11434/api/tags")
                models = response.json().get('models', [])
                if models:
                    print("\nAvailable models:")
                    for i, model in enumerate(models):
                        print(f"{i+1}. {model['name']}")
                    
                    try:
                        idx = int(input("Select model number: ")) - 1
                        if 0 <= idx < len(models):
                            test_ollama_model(models[idx]['name'])
                        else:
                            print("Invalid selection")
                    except ValueError:
                        print("Invalid input")
                else:
                    print("No models available")
            except Exception as e:
                print(f"Error: {e}")
        elif choice == "4":
            print("Goodbye! 👋")
            break
        else:
            print("❌ Invalid choice. Please try again.")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nExiting...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
