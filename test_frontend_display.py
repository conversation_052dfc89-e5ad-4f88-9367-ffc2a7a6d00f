#!/usr/bin/env python3
"""
使用Playwright检查前端数据展示情况
"""

import asyncio
import time
from playwright.async_api import async_playwright

async def test_frontend_display():
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🌐 正在访问应用...")
            await page.goto("http://localhost:8000")
            
            # 等待页面加载
            await page.wait_for_load_state("networkidle")
            await asyncio.sleep(2)
            
            print("📸 截取初始页面...")
            await page.screenshot(path="screenshots/01_initial_page.png", full_page=True)
            
            # 检查是否有AI Chat标签
            ai_chat_tab = page.locator('[data-tab="ai-chat"]')
            if await ai_chat_tab.count() > 0:
                print("✅ 找到AI Chat标签")
                await ai_chat_tab.click()
                await asyncio.sleep(1)
            
            # 提交一个AI查询
            print("🤖 提交AI查询...")
            question_input = page.locator('#question-input')
            await question_input.fill("What tables do we have?")
            
            # 点击提交按钮
            submit_button = page.locator('#ask-button')
            await submit_button.click()
            
            print("⏳ 等待任务执行...")
            await asyncio.sleep(2)
            await page.screenshot(path="screenshots/02_query_submitted.png", full_page=True)
            
            # 切换到Analytics标签查看任务进度
            analytics_tab = page.locator('[data-tab="analytics"]')
            if await analytics_tab.count() > 0:
                print("📊 切换到Analytics标签...")
                await analytics_tab.click()
                await asyncio.sleep(1)
                await page.screenshot(path="screenshots/03_analytics_tab.png", full_page=True)
            
            # 等待任务完成（最多30秒）
            print("⏳ 等待任务完成...")
            for i in range(30):
                # 检查是否有完成的任务
                completed_tasks = page.locator('.analytics-card:has-text("✅")')
                if await completed_tasks.count() > 0:
                    print(f"✅ 任务完成！（等待了{i+1}秒）")
                    break
                await asyncio.sleep(1)
            
            await page.screenshot(path="screenshots/04_task_completed.png", full_page=True)
            
            # 切换回AI Chat标签查看结果
            print("🔄 切换回AI Chat标签查看结果...")
            ai_chat_tab = page.locator('[data-tab="ai-chat"]')
            await ai_chat_tab.click()
            await asyncio.sleep(2)
            
            await page.screenshot(path="screenshots/05_results_display.png", full_page=True)
            
            # 检查结果容器
            results_container = page.locator('#results-container')
            if await results_container.count() > 0:
                print("✅ 找到结果容器")
                
                # 检查是否有结果卡片
                result_cards = page.locator('.card.border-left-accent')
                card_count = await result_cards.count()
                print(f"📋 找到 {card_count} 个结果卡片")
                
                if card_count > 0:
                    # 检查第一个结果卡片的内容
                    first_card = result_cards.first
                    card_text = await first_card.inner_text()
                    print("📝 第一个结果卡片内容:")
                    print("=" * 50)
                    print(card_text)
                    print("=" * 50)
                    
                    # 检查是否有数据表格
                    tables = page.locator('table')
                    table_count = await tables.count()
                    print(f"📊 找到 {table_count} 个数据表格")
                    
                    if table_count > 0:
                        # 获取表格内容
                        table_text = await tables.first.inner_text()
                        print("📊 表格内容:")
                        print("=" * 50)
                        print(table_text)
                        print("=" * 50)
                    else:
                        print("❌ 没有找到数据表格")
                        
                    # 检查是否有导出按钮
                    export_buttons = page.locator('button:has-text("CSV"), button:has-text("JSON")')
                    export_count = await export_buttons.count()
                    print(f"💾 找到 {export_count} 个导出按钮")
                    
                else:
                    print("❌ 没有找到结果卡片")
            else:
                print("❌ 没有找到结果容器")
            
            # 检查控制台错误
            print("🔍 检查控制台消息...")
            
            # 等待一下让用户看到结果
            print("⏸️ 暂停10秒让您查看结果...")
            await asyncio.sleep(10)
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            await page.screenshot(path="screenshots/error.png", full_page=True)
            
        finally:
            await browser.close()

if __name__ == "__main__":
    # 创建截图目录
    import os
    os.makedirs("screenshots", exist_ok=True)
    
    print("🚀 开始前端显示测试...")
    asyncio.run(test_frontend_display())
    print("✅ 测试完成！请查看screenshots目录中的截图。")
