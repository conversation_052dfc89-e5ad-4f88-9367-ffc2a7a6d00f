#!/usr/bin/env python3
"""
Quick start script for AutoGen + ClickHouse demo.
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_requirements():
    """Check if required files exist."""
    required_files = [
        'requirements.txt',
        'config.py',
        'clickhouse_client.py',
        'agents.py',
        'setup_database.py',
        'main.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files found")
    return True

def check_env_file():
    """Check if .env file exists."""
    if not Path('.env').exists():
        if Path('.env.example').exists():
            print("⚠️  .env file not found. Please copy .env.example to .env and configure it.")
            print("   cp .env.example .env")
            return False
        else:
            print("❌ Neither .env nor .env.example found")
            return False
    
    print("✅ .env file found")
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print(f"Error output: {e.stderr}")
        return False

def start_clickhouse():
    """Start ClickHouse using Docker Compose."""
    if not Path('docker-compose.yml').exists():
        print("⚠️  docker-compose.yml not found. Please start ClickHouse manually.")
        return True
    
    print("🐳 Starting ClickHouse with Docker Compose...")
    try:
        # Check if Docker is available
        subprocess.run(['docker', '--version'], check=True, capture_output=True)
        subprocess.run(['docker-compose', '--version'], check=True, capture_output=True)
        
        # Start ClickHouse
        subprocess.run(['docker-compose', 'up', '-d'], check=True)
        print("✅ ClickHouse started successfully")
        
        # Wait for ClickHouse to be ready
        print("⏳ Waiting for ClickHouse to be ready...")
        time.sleep(10)
        
        return True
    except subprocess.CalledProcessError:
        print("⚠️  Docker/Docker Compose not available. Please start ClickHouse manually.")
        return True
    except FileNotFoundError:
        print("⚠️  Docker not found. Please start ClickHouse manually.")
        return True

def setup_database():
    """Set up the database with sample data."""
    print("🗄️  Setting up database...")
    try:
        result = subprocess.run([sys.executable, 'setup_database.py'], 
                              capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ Database setup completed successfully")
            return True
        else:
            print(f"❌ Database setup failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ Database setup timed out")
        return False
    except Exception as e:
        print(f"❌ Database setup error: {e}")
        return False

def run_tests():
    """Run basic tests."""
    print("🧪 Running tests...")
    try:
        result = subprocess.run([sys.executable, 'test_demo.py'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ Tests passed")
            return True
        else:
            print(f"⚠️  Some tests failed: {result.stdout}")
            return True  # Don't fail the setup for test failures
    except Exception as e:
        print(f"⚠️  Test execution error: {e}")
        return True  # Don't fail the setup for test errors

def main():
    """Main quick start function."""
    print("🚀 AutoGen + ClickHouse Demo Quick Start")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Setup failed: Missing required files")
        return 1
    
    # Check environment file
    if not check_env_file():
        print("\n❌ Setup failed: Please configure .env file")
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed: Could not install dependencies")
        return 1
    
    # Start ClickHouse
    start_clickhouse()
    
    # Setup database
    if not setup_database():
        print("\n❌ Setup failed: Could not setup database")
        print("Please check your ClickHouse connection and try again.")
        return 1
    
    # Run tests
    run_tests()
    
    # Success message
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run the demo: python main.py")
    print("2. Open Jupyter notebook: jupyter notebook demo_notebook.ipynb")
    print("3. Explore the code and customize as needed")
    print("\nFor help, check the README.md file.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
