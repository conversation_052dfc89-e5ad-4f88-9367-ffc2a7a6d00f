#!/usr/bin/env python3
"""
Test the web service using <PERSON><PERSON>
"""

import asyncio
import time
import requests
from playwright.async_api import async_playwright

async def test_web_service():
    """Test the web service functionality."""
    
    # First, check if the service is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"✅ Health check: {response.status_code}")
        print(f"Health response: {response.json()}")
    except Exception as e:
        print(f"❌ Service not accessible: {e}")
        return
    
    # Test database info endpoint
    try:
        response = requests.get("http://localhost:8000/api/database-info", timeout=10)
        print(f"✅ Database info: {response.status_code}")
        if response.status_code == 200:
            db_info = response.json()
            print(f"Tables: {db_info.get('tables', [])}")
            print(f"Total records: {db_info.get('total_records', {})}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Database info failed: {e}")
        return
    
    # Test query endpoint
    try:
        query_data = {
            "question": "What tables do we have in the database?",
            "include_raw_data": True
        }
        response = requests.post(
            "http://localhost:8000/api/query", 
            json=query_data, 
            timeout=30
        )
        print(f"✅ Query test: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Answer: {result.get('answer', 'No answer')[:200]}...")
            print(f"Execution time: {result.get('execution_time', 0):.2f}s")
        else:
            print(f"Query error: {response.text}")
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return
    
    # Now test with Playwright
    async with async_playwright() as p:
        print("\n🎭 Starting Playwright frontend interaction test...")

        # Launch browser (set headless=False to see the interaction)
        browser = await p.chromium.launch(headless=False, slow_mo=500)  # Slow down for visibility
        page = await browser.new_page()

        # Enable console logging
        page.on("console", lambda msg: print(f"🖥️  Console: {msg.text}"))
        page.on("pageerror", lambda error: print(f"❌ Page error: {error}"))

        try:
            # Navigate to the web service
            print("📱 Navigating to web service...")
            await page.goto("http://localhost:8000")

            # Wait for page to load
            print("⏳ Waiting for page elements to load...")
            await page.wait_for_selector("h1", timeout=10000)

            # Check if the page loaded correctly
            title = await page.text_content("h1")
            print(f"✅ Page title: {title.strip()}")

            # Wait for Alpine.js to initialize
            print("⏳ Waiting for Alpine.js to initialize...")
            await page.wait_for_timeout(2000)

            # Check Alpine.js state
            alpine_loaded = await page.evaluate("() => typeof window.Alpine !== 'undefined'")
            print(f"Alpine.js loaded: {alpine_loaded}")

            # Check if database info is displayed
            print("🔍 Checking database info...")
            try:
                # Wait for database info to load
                await page.wait_for_selector("[x-show='dbInfo']", timeout=5000)
                db_info_visible = await page.is_visible("[x-show='dbInfo']")
                print(f"Database info visible: {db_info_visible}")

                if db_info_visible:
                    table_elements = await page.query_selector_all(".bg-blue-50")
                    print(f"✅ Found {len(table_elements)} table info cards")
                else:
                    print("⚠️  Database info not visible")
            except:
                print("⚠️  Database info section not found")

            # Test query functionality
            print("\n💬 Testing query functionality...")

            # Find the input field
            input_selector = "input[placeholder*='Ask a question']"
            print(f"🔍 Looking for input field: {input_selector}")

            await page.wait_for_selector(input_selector, timeout=5000)
            input_field = await page.query_selector(input_selector)

            if input_field:
                print("✅ Found input field")

                # Check if input is enabled
                is_disabled = await page.is_disabled(input_selector)
                print(f"Input field disabled: {is_disabled}")

                # Type a question
                test_question = "What tables do we have in the database?"
                print(f"✏️ Typing question: '{test_question}'")
                await page.fill(input_selector, test_question)

                # Verify the text was entered
                input_value = await page.input_value(input_selector)
                print(f"✅ Question entered: '{input_value}'")

                # Find the Ask button
                button_selector = "button:has-text('Ask')"
                print(f"🔍 Looking for Ask button: {button_selector}")

                ask_button = await page.query_selector(button_selector)
                if ask_button:
                    print("✅ Found Ask button")

                    # Check if button is enabled
                    is_button_disabled = await page.is_disabled(button_selector)
                    print(f"Ask button disabled: {is_button_disabled}")

                    # Get button text
                    button_text = await page.text_content(button_selector)
                    print(f"Button text: '{button_text.strip()}'")

                    # Click the button
                    print("🖱️ Clicking Ask button...")
                    await page.click(button_selector, force=True)
                    print("✅ Button clicked")

                    # Check for loading state
                    print("⏳ Checking for loading state...")
                    await page.wait_for_timeout(1000)

                    loading_visible = await page.is_visible("text=AI is analyzing")
                    print(f"Loading indicator visible: {loading_visible}")

                    # Wait for response
                    print("⏳ Waiting for AI response (30 seconds max)...")

                    # Wait and check periodically
                    for i in range(30):  # Check every second for 30 seconds
                        await page.wait_for_timeout(1000)

                        # Check for results
                        result_containers = await page.query_selector_all(".bg-white.rounded-lg.shadow-md")

                        # Check for error
                        error_visible = await page.is_visible(".bg-red-50")

                        if len(result_containers) > 3:  # More than just header containers
                            print(f"✅ Found {len(result_containers)} result containers after {i+1} seconds")
                            break
                        elif error_visible:
                            error_text = await page.text_content(".text-red-700")
                            print(f"❌ Error appeared: {error_text}")
                            break
                        elif i % 5 == 0:  # Print progress every 5 seconds
                            print(f"⏳ Still waiting... ({i+1}/30 seconds)")

                    # Final check for results
                    final_results = await page.query_selector_all(".bg-white.rounded-lg.shadow-md")
                    print(f"📊 Final result count: {len(final_results)}")

                    # Look for specific content
                    page_content = await page.content()
                    if "customers" in page_content and "products" in page_content and "sales" in page_content:
                        print("✅ Response contains expected table names")
                    elif "tables" in page_content.lower():
                        print("✅ Response mentions tables")
                    else:
                        print("⚠️  Expected content not found in response")

                    # Check for any AI response text
                    ai_responses = await page.query_selector_all("text=🤖")
                    if ai_responses:
                        print(f"✅ Found {len(ai_responses)} AI response indicators")

                else:
                    print("❌ Ask button not found")
            else:
                print("❌ Input field not found")

            # Take a screenshot
            await page.screenshot(path="web_service_test.png", full_page=True)
            print("📸 Screenshot saved as web_service_test.png")

            # Keep browser open for manual inspection
            print("⏳ Keeping browser open for 10 seconds for manual inspection...")
            await page.wait_for_timeout(10000)

        except Exception as e:
            print(f"❌ Playwright test error: {e}")
            await page.screenshot(path="error_screenshot.png", full_page=True)
            print("📸 Error screenshot saved")

        finally:
            await browser.close()
            print("🎭 Browser closed")

async def main():
    """Main test function."""
    print("🧪 Testing AutoGen + ClickHouse Web Service")
    print("=" * 50)
    
    await test_web_service()
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")

if __name__ == "__main__":
    asyncio.run(main())
