# 💾 数据下载功能实现

## 🎯 功能概述

我们实现了完整的数据下载功能，用户可以根据对应的SQL查询下载数据，支持CSV和JSON两种格式的文件下载。

## 🏗️ 系统架构

### 后端API设计
```
POST /api/download-data
├── 接收SQL查询和格式参数
├── 执行SQL查询获取数据
├── 根据格式生成文件内容
└── 返回文件下载响应
```

### 前端下载流程
```
用户点击下载按钮
├── 获取对应结果的SQL查询
├── 发送API请求到后端
├── 显示加载状态
├── 接收文件响应
└── 触发浏览器下载
```

## 🔧 后端实现

### 1. 下载API端点
```python
@app.post("/api/download-data")
async def download_data(request: dict):
    """Download data in specified format based on SQL query."""
    try:
        sql = request.get('sql')
        format_type = request.get('format', 'csv').lower()
        filename = request.get('filename')
        
        if not sql:
            raise HTTPException(status_code=400, detail="SQL query is required")
        
        # Execute the SQL query
        result = execute_sql_safely(sql)
        data = result.get('data', [])
        
        if not data:
            raise HTTPException(status_code=404, detail="No data returned from query")
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"query_result_{timestamp}.{format_type}"
        
        # Generate content based on format
        if format_type == 'csv':
            content = generate_csv_content(data)
            media_type = 'text/csv'
        elif format_type == 'json':
            content = json.dumps(data, indent=2, default=str)
            media_type = 'application/json'
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
        
        # Return file response
        return Response(
            content=content,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
        
    except Exception as e:
        logger.error(f"Download data error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

### 2. CSV生成器
```python
def generate_csv_content(data: list) -> str:
    """Generate CSV content from data list."""
    if not data:
        return ''
    
    import csv
    import io
    
    output = io.StringIO()
    fieldnames = data[0].keys()
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()
    writer.writerows(data)
    
    return output.getvalue()
```

## 🎨 前端实现

### 1. 下载按钮更新
```html
<!-- 在结果展示中添加下载按钮 -->
<div class="flex space-x-3">
    <button onclick="downloadQueryData(${index}, 'csv')"
            class="export-btn-csv flex items-center">
        <i class="material-icons text-sm mr-1">file_download</i>
        CSV
    </button>
    <button onclick="downloadQueryData(${index}, 'json')"
            class="export-btn-json flex items-center">
        <i class="material-icons text-sm mr-1">code</i>
        JSON
    </button>
</div>
```

### 2. 下载函数实现
```javascript
async function downloadQueryData(index, format) {
    console.log('📥 Downloading data:', index, format);
    
    try {
        // Get the result data from the global results array
        if (!window.app || !window.app.results || !window.app.results[index]) {
            showError('No data found to download');
            return;
        }
        
        const result = window.app.results[index];
        const sql = result.sql;
        
        if (!sql) {
            showError('No SQL query found for this result');
            return;
        }
        
        // Show loading state
        const buttons = document.querySelectorAll(`button[onclick*="downloadQueryData(${index}, '${format}')"]`);
        let button = null;
        let originalText = '';
        
        if (buttons.length > 0) {
            button = buttons[0];
            originalText = button.innerHTML;
            button.innerHTML = '<i class="material-icons text-sm mr-1 animate-spin">refresh</i>Loading...';
            button.disabled = true;
        }
        
        // Make API request to download data
        const response = await fetch('/api/download-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                sql: sql,
                format: format,
                filename: `query_result_${Date.now()}.${format}`
            })
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `Download failed: ${response.status}`);
        }
        
        // Get the filename from response headers
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `query_result_${Date.now()}.${format}`;
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename=(.+)/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }
        
        // Create blob and download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        console.log('✅ Download completed:', filename);
        showSuccess(`Downloaded ${filename} successfully!`);
        
    } catch (error) {
        console.error('❌ Download error:', error);
        showError(`Download failed: ${error.message}`);
    } finally {
        // Restore button state
        if (button) {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }
}
```

### 3. 成功提示功能
```javascript
function showSuccess(message) {
    // Create or update success notification
    let successElement = document.getElementById('success-notification');
    if (!successElement) {
        successElement = document.createElement('div');
        successElement.id = 'success-notification';
        successElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        document.body.appendChild(successElement);
    }
    
    successElement.textContent = message;
    successElement.classList.remove('translate-x-full');
    
    // Auto hide after 3 seconds
    setTimeout(() => {
        successElement.classList.add('translate-x-full');
    }, 3000);
}
```

## 🎯 功能特性

### 1. 支持的下载格式
- **CSV格式**: 适合Excel和数据分析工具
- **JSON格式**: 适合程序处理和API集成

### 2. 智能文件命名
- 自动生成带时间戳的文件名
- 格式: `query_result_YYYYMMDD_HHMMSS.csv`
- 支持自定义文件名

### 3. 用户体验优化
- **加载状态**: 下载时显示加载动画
- **成功提示**: 下载完成后显示成功消息
- **错误处理**: 详细的错误信息提示
- **按钮状态**: 下载期间禁用按钮防止重复点击

### 4. 安全性考虑
- **SQL验证**: 后端验证SQL查询的安全性
- **数据检查**: 确保有数据才允许下载
- **错误处理**: 完善的异常处理机制

## 🔍 使用流程

### 1. 用户操作流程
```
1. 用户提交AI查询或SQL查询
2. 查看查询结果和数据表格
3. 点击CSV或JSON下载按钮
4. 系统显示加载状态
5. 浏览器自动下载文件
6. 显示下载成功提示
```

### 2. 系统处理流程
```
1. 前端获取对应结果的SQL查询
2. 发送POST请求到/api/download-data
3. 后端执行SQL查询获取最新数据
4. 根据格式生成文件内容
5. 设置正确的Content-Type和文件名
6. 返回文件下载响应
7. 前端触发浏览器下载
```

## 🎨 文件格式示例

### CSV格式示例
```csv
name,rows
customers,500
sales,1000
products,200
traffic_logs_with_geo,5000
```

### JSON格式示例
```json
[
  {
    "name": "customers",
    "rows": 500
  },
  {
    "name": "sales", 
    "rows": 1000
  },
  {
    "name": "products",
    "rows": 200
  },
  {
    "name": "traffic_logs_with_geo",
    "rows": 5000
  }
]
```

## 🚀 技术优势

### 1. 服务器端处理
- **实时数据**: 每次下载都执行最新的SQL查询
- **性能优化**: 服务器端生成文件，减少前端负担
- **格式标准**: 使用标准的CSV和JSON格式

### 2. 前端体验
- **无刷新下载**: 使用Blob API实现无刷新下载
- **进度反馈**: 清晰的加载状态和成功提示
- **错误处理**: 友好的错误信息显示

### 3. 扩展性
- **格式扩展**: 易于添加新的下载格式（如Excel）
- **权限控制**: 可以添加用户权限验证
- **批量下载**: 可以扩展支持批量下载功能

## 🔧 故障排除

### 常见问题
1. **下载失败**: 检查SQL查询是否有效
2. **文件为空**: 确认查询返回了数据
3. **格式错误**: 验证请求的格式参数
4. **权限问题**: 检查API访问权限

### 调试方法
1. 查看浏览器控制台的错误信息
2. 检查网络请求的响应状态
3. 验证SQL查询的正确性
4. 确认数据格式的完整性

这个数据下载功能为用户提供了便捷的数据导出能力，支持多种格式，具有良好的用户体验和扩展性！
