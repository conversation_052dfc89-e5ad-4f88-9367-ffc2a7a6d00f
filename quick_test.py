#!/usr/bin/env python3
"""
快速测试新功能
"""

import requests
import json

def test_features():
    """测试新功能"""
    
    print("🧪 快速测试新功能")
    print("=" * 40)
    
    # 测试prompt优化
    print("\n1. 测试Prompt优化")
    payload1 = {
        "question": "分析数据",
        "optimize_prompt": True,
        "enable_task_decomposition": False,
        "include_raw_data": True,
        "session_id": "test_opt"
    }
    
    try:
        response1 = requests.post("http://localhost:8000/api/query", json=payload1, timeout=60)
        if response1.status_code == 200:
            result1 = response1.json()
            raw_data = result1.get('raw_data', {})
            original = raw_data.get('original_question', '')
            processed = raw_data.get('processed_question', '')
            
            print(f"原始问题: {original}")
            print(f"优化问题: {processed}")
            print(f"状态: {'✅ 优化成功' if original != processed else '⚠️ 未优化'}")
        else:
            print(f"❌ 失败: {response1.status_code}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    # 测试任务拆解
    print("\n2. 测试任务拆解")
    payload2 = {
        "question": "创建业务报告包括销售分析和客户分析",
        "optimize_prompt": False,
        "enable_task_decomposition": True,
        "include_raw_data": True,
        "session_id": "test_decomp"
    }
    
    try:
        response2 = requests.post("http://localhost:8000/api/query", json=payload2, timeout=120)
        if response2.status_code == 200:
            result2 = response2.json()
            raw_data = result2.get('raw_data', {})
            sub_tasks = raw_data.get('sub_tasks', [])
            
            print(f"拆解结果: {len(sub_tasks)} 个子任务")
            for i, task in enumerate(sub_tasks[:3], 1):  # 只显示前3个
                print(f"  {i}. {task}")
            if len(sub_tasks) > 3:
                print(f"  ... 还有 {len(sub_tasks) - 3} 个子任务")
            
            print(f"状态: {'✅ 拆解成功' if len(sub_tasks) > 1 else '⚠️ 未拆解'}")
        else:
            print(f"❌ 失败: {response2.status_code}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n" + "=" * 40)
    print("📊 测试完成")

if __name__ == "__main__":
    test_features()
