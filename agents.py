"""
AutoGen agents for ClickHouse database interaction and analysis.
"""

import asyncio
from typing import Dict, Any, List, Optional
import pandas as pd
import json
from clickhouse_client import clickhouse_client
from config import LLM_CONFIG, LLM_PROVIDER, AGENT_CONFIG
import logging

# New AutoGen imports
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient

logger = logging.getLogger(__name__)

# LLM Configuration
def get_llm_config_for_autogen():
    """Get LLM configuration formatted for AutoGen."""
    if LLM_PROVIDER == "ollama":
        return {
            "config_list": [{
                "model": LLM_CONFIG["model"],
                "base_url": LLM_CONFIG["base_url"],
                "api_key": LLM_CONFIG["api_key"],
                "temperature": LLM_CONFIG["temperature"],
            }],
            "temperature": LLM_CONFIG["temperature"],
        }
    else:
        # OpenAI or Azure configuration
        return {
            "config_list": [LLM_CONFIG],
            "temperature": LLM_CONFIG["temperature"],
        }

llm_config = get_llm_config_for_autogen()

def execute_clickhouse_query(query: str) -> str:
    """
    Execute a ClickHouse query and return results as formatted string.

    Args:
        query: SQL query to execute

    Returns:
        Formatted query results or error message
    """
    try:
        # Validate query (basic security check)
        query_lower = query.lower().strip()
        if not query_lower.startswith('select'):
            return "Error: Only SELECT queries are allowed for security reasons."

        # Execute query
        result_df = clickhouse_client.execute_query(query)

        if result_df.empty:
            return "Query executed successfully but returned no results."

        # Format results
        if len(result_df) > 20:
            formatted_result = f"Query returned {len(result_df)} rows. Showing first 20:\n\n"
            formatted_result += result_df.head(20).to_string(index=False)
            formatted_result += f"\n\n... and {len(result_df) - 20} more rows."
        else:
            formatted_result = f"Query returned {len(result_df)} rows:\n\n"
            formatted_result += result_df.to_string(index=False)

        return formatted_result

    except Exception as e:
        error_msg = f"Query execution failed: {str(e)}"
        logger.error(error_msg)
        return error_msg

def get_database_schema() -> str:
    """Get database schema information."""
    try:
        tables = clickhouse_client.get_tables()
        schema_info = "Database Schema:\n\n"

        for table in tables:
            schema_info += f"Table: {table}\n"
            schema = clickhouse_client.get_table_schema(table)
            for column in schema:
                schema_info += f"  - {column['name']}: {column['type']}\n"
            schema_info += "\n"

        return schema_info
    except Exception as e:
        return f"Error retrieving schema: {str(e)}"

def get_table_sample(table_name: str, limit: int = 5) -> str:
    """Get sample data from a table."""
    try:
        sample_df = clickhouse_client.get_table_sample(table_name, limit)
        return f"Sample data from {table_name}:\n\n{sample_df.to_string(index=False)}"
    except Exception as e:
        return f"Error retrieving sample data: {str(e)}"

# Create specialized agents
def create_data_agent() -> autogen.AssistantAgent:
    """Create a data agent specialized in database queries."""
    system_message = """
    You are a Data Agent specialized in ClickHouse database operations. Your responsibilities:

    1. Execute SQL queries on ClickHouse database
    2. Retrieve and format data for analysis
    3. Provide database schema information
    4. Suggest optimized queries for better performance

    Available functions:
    - execute_clickhouse_query: Execute SELECT queries
    - get_database_schema: Get table schemas
    - get_table_sample: Get sample data from tables

    Always validate queries before execution and provide clear, formatted results.
    When writing queries, consider ClickHouse-specific optimizations like:
    - Using appropriate ORDER BY clauses
    - Leveraging ClickHouse functions for aggregations
    - Using LIMIT for large result sets

    When a user asks for data, first check the schema if needed, then write and execute appropriate queries.
    """

    return autogen.AssistantAgent(
        name="DataAgent",
        system_message=system_message,
        llm_config=llm_config,
        **AGENT_CONFIG
    )

def create_analyst_agent() -> autogen.AssistantAgent:
    """Create an analyst agent for data analysis and insights."""
    system_message = """
    You are an Analyst Agent specialized in data analysis and business insights. Your responsibilities:

    1. Analyze data retrieved from ClickHouse database
    2. Generate business insights and recommendations
    3. Create summaries and reports
    4. Identify trends and patterns in data

    When analyzing data:
    - Look for trends, patterns, and anomalies
    - Provide actionable business insights
    - Suggest follow-up questions or analyses
    - Present findings in a clear, business-friendly format

    Work with the Data Agent to get the data you need for analysis.
    Focus on providing valuable insights rather than just describing the data.
    """

    return autogen.AssistantAgent(
        name="AnalystAgent",
        system_message=system_message,
        llm_config=llm_config,
        **AGENT_CONFIG
    )

def create_user_proxy() -> autogen.UserProxyAgent:
    """Create a user proxy agent for human interaction."""
    return autogen.UserProxyAgent(
        name="UserProxy",
        human_input_mode="ALWAYS",  # Change to "NEVER" for automated mode
        max_consecutive_auto_reply=AGENT_CONFIG["max_consecutive_auto_reply"],
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        code_execution_config=False,
    )

# Agent factory
class AgentFactory:
    """Factory class for creating and managing agents."""

    @staticmethod
    def create_agents() -> Dict[str, autogen.ConversableAgent]:
        """Create all agents and return as dictionary."""
        return {
            "data_agent": create_data_agent(),
            "analyst_agent": create_analyst_agent(),
            "user_proxy": create_user_proxy()
        }

    @staticmethod
    def create_group_chat(agents: Dict[str, autogen.ConversableAgent]) -> autogen.GroupChat:
        """Create a group chat with all agents."""
        agent_list = list(agents.values())

        return autogen.GroupChat(
            agents=agent_list,
            messages=[],
            max_round=20,
            speaker_selection_method="round_robin"
        )

    @staticmethod
    def create_group_chat_manager(group_chat: autogen.GroupChat) -> autogen.GroupChatManager:
        """Create a group chat manager."""
        return autogen.GroupChatManager(
            groupchat=group_chat,
            llm_config=llm_config
        )

# Create specialized agents
def create_data_agent() -> autogen.AssistantAgent:
    """Create a data agent specialized in database queries."""
    system_message = """
    You are a Data Agent specialized in ClickHouse database operations. Your responsibilities:
    
    1. Execute SQL queries on ClickHouse database
    2. Retrieve and format data for analysis
    3. Provide database schema information
    4. Suggest optimized queries for better performance
    
    Available functions:
    - execute_clickhouse_query: Execute SELECT queries
    - get_database_schema: Get table schemas
    - get_table_sample: Get sample data from tables
    
    Always validate queries before execution and provide clear, formatted results.
    When writing queries, consider ClickHouse-specific optimizations like:
    - Using appropriate ORDER BY clauses
    - Leveraging ClickHouse functions for aggregations
    - Using LIMIT for large result sets
    """
    
    return autogen.AssistantAgent(
        name="DataAgent",
        system_message=system_message,
        llm_config=llm_config,
        **AGENT_CONFIG
    )

def create_analyst_agent() -> autogen.AssistantAgent:
    """Create an analyst agent for data analysis and insights."""
    system_message = """
    You are an Analyst Agent specialized in data analysis and business insights. Your responsibilities:
    
    1. Analyze data retrieved from ClickHouse database
    2. Generate business insights and recommendations
    3. Create summaries and reports
    4. Identify trends and patterns in data
    
    When analyzing data:
    - Look for trends, patterns, and anomalies
    - Provide actionable business insights
    - Suggest follow-up questions or analyses
    - Present findings in a clear, business-friendly format
    
    Work with the Data Agent to get the data you need for analysis.
    """
    
    return autogen.AssistantAgent(
        name="AnalystAgent",
        system_message=system_message,
        llm_config=llm_config,
        **AGENT_CONFIG
    )

def create_user_proxy() -> autogen.UserProxyAgent:
    """Create a user proxy agent for human interaction."""
    return autogen.UserProxyAgent(
        name="UserProxy",
        human_input_mode="ALWAYS",  # Change to "NEVER" for automated mode
        max_consecutive_auto_reply=AGENT_CONFIG["max_consecutive_auto_reply"],
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        code_execution_config=False,
    )

# Agent factory
class AgentFactory:
    """Factory class for creating and managing agents."""
    
    @staticmethod
    def create_agents() -> Dict[str, autogen.ConversableAgent]:
        """Create all agents and return as dictionary."""
        return {
            "data_agent": create_data_agent(),
            "analyst_agent": create_analyst_agent(),
            "user_proxy": create_user_proxy()
        }
    
    @staticmethod
    def create_group_chat(agents: Dict[str, autogen.ConversableAgent]) -> autogen.GroupChat:
        """Create a group chat with all agents."""
        agent_list = list(agents.values())
        
        return autogen.GroupChat(
            agents=agent_list,
            messages=[],
            max_round=20,
            speaker_selection_method="round_robin"
        )
    
    @staticmethod
    def create_group_chat_manager(group_chat: autogen.GroupChat) -> autogen.GroupChatManager:
        """Create a group chat manager."""
        return autogen.GroupChatManager(
            groupchat=group_chat,
            llm_config=llm_config
        )
