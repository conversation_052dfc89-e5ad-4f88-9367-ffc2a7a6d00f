// NEW Utils file to bypass cache - Version 2025-08-01-14:25
console.log('🚀 NEW Utils.js loaded successfully!');

// Add CSS animations and styles
const animationStyles = `
<style>
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.table-hover-row:hover {
    background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
    transform: translateX(2px);
    transition: all 0.2s ease;
}

.gradient-border {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
    padding: 2px;
    border-radius: 12px;
}

.gradient-border-inner {
    background: white;
    border-radius: 10px;
}
</style>
`;

// Inject styles if not already present
if (!document.querySelector('#complex-task-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'complex-task-styles';
    styleElement.innerHTML = animationStyles;
    document.head.appendChild(styleElement);
}

// Enhanced table generator with modern styling
function createDataTableNew(data) {
    console.log('🔥 NEW createDataTableNew called with:', data);

    if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('❌ No valid data');
        return `
            <div class="text-center py-12">
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8 border border-gray-200">
                    <i class="material-icons text-6xl text-gray-400 mb-4">inbox</i>
                    <h3 class="text-lg font-medium text-gray-600 mb-2">暂无数据</h3>
                    <p class="text-gray-500 text-sm">查询未返回任何结果</p>
                </div>
            </div>
        `;
    }

    const firstRow = data[0];
    if (!firstRow || typeof firstRow !== 'object') {
        console.log('❌ Invalid first row');
        return `
            <div class="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-xl border border-red-200">
                <div class="flex items-center">
                    <i class="material-icons text-red-500 mr-3">error</i>
                    <div>
                        <h3 class="font-medium text-red-800">数据格式错误</h3>
                        <p class="text-red-600 text-sm">无法解析返回的数据格式</p>
                    </div>
                </div>
            </div>
        `;
    }

    const columns = Object.keys(firstRow);
    const maxRows = 15;
    const displayData = data.slice(0, maxRows);
    const hasMoreRows = data.length > maxRows;

    console.log('🔥 Columns found:', columns);

    let html = `
        <div class="gradient-border">
            <div class="gradient-border-inner overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gradient-to-r from-blue-50 to-indigo-50">
                            <tr>
                                ${columns.map(col => `
                                    <th class="px-6 py-4 text-left font-semibold text-gray-700 border-b border-gray-200">
                                        <div class="flex items-center">
                                            <i class="material-icons text-sm mr-2 text-blue-500">table_chart</i>
                                            <span class="text-sm uppercase tracking-wider">${col}</span>
                                        </div>
                                    </th>
                                `).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${displayData.map((row, index) => `
                                <tr class="table-hover-row ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}">
                                    ${columns.map(col => `
                                        <td class="px-6 py-4 border-b border-gray-100">
                                            <div class="text-sm font-medium text-gray-800">
                                                ${row[col] !== null && row[col] !== undefined ?
                                                    (typeof row[col] === 'string' && row[col].length > 50 ?
                                                        `<span title="${row[col]}">${row[col].substring(0, 50)}...</span>` :
                                                        row[col]
                                                    ) :
                                                    '<span class="text-gray-400 italic">null</span>'
                                                }
                                            </div>
                                        </td>
                                    `).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                ${hasMoreRows ? `
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-center">
                            <div class="bg-white px-4 py-2 rounded-full shadow-sm border border-blue-200">
                                <div class="flex items-center text-blue-700">
                                    <i class="material-icons text-sm mr-2">info</i>
                                    <span class="text-sm font-medium">显示前 ${maxRows} 行，共 ${data.length} 行数据</span>
                                </div>
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    console.log('🔥 NEW table HTML generated successfully!');
    return html;
}

// Override the displayResults function
function displayResultsNew(results) {
    console.log('🔥 NEW displayResults called with:', results);

    if (!results || results.length === 0) {
        document.getElementById('no-results').style.display = 'block';
        document.getElementById('results-container').style.display = 'none';
        return;
    }

    document.getElementById('no-results').style.display = 'none';
    document.getElementById('results-container').style.display = 'block';

    const resultsContainer = document.getElementById('results-list');
    resultsContainer.innerHTML = '';

    results.forEach((result, index) => {
        console.log(`🔥 Processing result ${index}:`, result);

        // Check if this is a complex task decomposition result
        if (result.task_decomposition_used && result.sub_tasks) {
            createComplexTaskCard(result, index, resultsContainer);
        } else {
            createSimpleTaskCard(result, index, resultsContainer);
        }
    });

    console.log('🔥 NEW displayResults completed successfully!');
}

// Create card for complex task decomposition results
function createComplexTaskCard(result, index, container) {
    const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';

    const resultCard = document.createElement('div');
    resultCard.className = 'bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 hover:shadow-xl transition-all duration-300';

    // Parse the complex answer to extract sub-tasks
    const subTasks = parseComplexTaskAnswer(result.answer);

    resultCard.innerHTML = `
        <!-- Header Section -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <div class="bg-gradient-to-r from-purple-500 to-indigo-600 p-3 rounded-xl shadow-lg mr-4">
                    <i class="material-icons text-white text-xl">account_tree</i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800 mb-1">智能任务分析</h3>
                    <div class="flex items-center space-x-2">
                        <span class="bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                            ${result.sub_tasks.length} 个子任务
                        </span>
                        <span class="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="material-icons text-xs mr-1">check_circle</i>已完成
                        </span>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-gray-500 text-sm">${timestamp}</div>
                <div class="text-purple-600 text-sm font-medium mt-1">
                    <i class="material-icons text-xs mr-1">timer</i>${(result.execution_time || 0).toFixed(1)}s
                </div>
            </div>
        </div>

        <!-- Original Question -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-5 rounded-xl mb-6 border border-blue-200">
            <div class="flex items-center mb-3">
                <div class="bg-blue-500 p-2 rounded-lg mr-3">
                    <i class="material-icons text-white text-sm">help_outline</i>
                </div>
                <h4 class="font-semibold text-blue-900">原始问题</h4>
            </div>
            <p class="text-blue-800 leading-relaxed">${result.question}</p>
        </div>

        <!-- Task Breakdown -->
        <div class="mb-6">
            <div class="flex items-center mb-5">
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 p-2 rounded-lg mr-3">
                    <i class="material-icons text-white text-sm">list_alt</i>
                </div>
                <h4 class="font-semibold text-gray-800">任务拆解</h4>
            </div>
            <div class="grid gap-3">
                ${result.sub_tasks.map((task, i) => `
                    <div class="group bg-white p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-200">
                        <div class="flex items-start">
                            <div class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-1 shadow-lg">
                                ${i + 1}
                            </div>
                            <div class="flex-1">
                                <div class="font-medium text-gray-800 mb-1">子任务 ${i + 1}</div>
                                <p class="text-gray-600 text-sm leading-relaxed">${task}</p>
                            </div>
                            <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <span class="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">
                                    <i class="material-icons text-xs">check</i>
                                </span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>

        <!-- Analysis Results -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-lg mr-3">
                        <i class="material-icons text-white text-sm">analytics</i>
                    </div>
                    <h4 class="font-semibold text-gray-800">分析结果</h4>
                </div>
                <button onclick="toggleComplexResults(${index})"
                        class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg">
                    <i class="material-icons text-sm mr-1">expand_more</i>查看详情
                </button>
            </div>

            <div id="complex-results-${index}" class="hidden">
                <div class="bg-gray-50 p-5 rounded-xl max-h-96 overflow-y-auto border border-gray-200">
                    ${formatComplexTaskResults(subTasks)}
                </div>
            </div>

            <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-5 rounded-xl border border-green-200">
                <div class="flex items-center mb-3">
                    <div class="bg-green-500 p-2 rounded-lg mr-3">
                        <i class="material-icons text-white text-sm">check_circle</i>
                    </div>
                    <span class="font-semibold text-green-800">分析完成</span>
                </div>
                <p class="text-green-700 text-sm leading-relaxed">
                    成功执行了 ${result.sub_tasks.length} 个子任务，提供了全面的数据分析结果。每个子任务都包含了具体的SQL查询和数据洞察。
                </p>
            </div>
        </div>

        <!-- Footer Stats -->
        <div class="bg-gray-50 p-4 rounded-xl border border-gray-200">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-6">
                    <div class="flex items-center text-gray-600">
                        <i class="material-icons text-sm mr-2">timeline</i>
                        <span class="text-sm">执行时间: ${(result.execution_time || 0).toFixed(2)}秒</span>
                    </div>
                    <div class="flex items-center text-gray-600">
                        <i class="material-icons text-sm mr-2">task_alt</i>
                        <span class="text-sm">${result.sub_tasks.length} 个子任务</span>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-800 px-4 py-2 rounded-full text-sm font-medium">
                    <i class="material-icons text-sm mr-1">psychology</i>
                    智能分析
                </div>
            </div>
        </div>
    `;

    container.appendChild(resultCard);
}

// Create card for simple task results
function createSimpleTaskCard(result, index, container) {
    const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';
    const formattedAnswer = result.answer || 'No answer received';

    let dataSection = '';
    if (result.data && result.data.length > 0) {
        console.log('🔥 Creating table for result data:', result.data);
        const tableHtml = createDataTableNew(result.data);

        dataSection = `
            <div class="mt-6">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <div class="bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg mr-3">
                            <i class="material-icons text-white text-sm">table_view</i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-800">查询结果</h4>
                        <span class="ml-3 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">${result.data.length} 行数据</span>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="downloadData(${index}, 'csv')" class="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-md hover:shadow-lg">
                            <i class="material-icons text-sm mr-1">download</i>CSV
                        </button>
                        <button onclick="downloadData(${index}, 'json')" class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg">
                            <i class="material-icons text-sm mr-1">download</i>JSON
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                    ${tableHtml}
                </div>
            </div>
        `;
    }

    const resultCard = document.createElement('div');
    resultCard.className = 'bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg p-6 mb-8 border border-gray-200 hover:shadow-xl transition-all duration-300';
    resultCard.innerHTML = `
        <!-- Header Section -->
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center">
                <div class="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl shadow-lg mr-4">
                    <i class="material-icons text-white text-xl">psychology</i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-800 mb-1">AI 查询分析</h3>
                    <div class="flex items-center space-x-2">
                        <span class="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                            标准查询
                        </span>
                        <span class="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="material-icons text-xs mr-1">check_circle</i>已完成
                        </span>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-gray-500 text-sm">${timestamp}</div>
                <div class="text-blue-600 text-sm font-medium mt-1">
                    <i class="material-icons text-xs mr-1">timer</i>${(result.execution_time || 0).toFixed(1)}s
                </div>
            </div>
        </div>

        <!-- Question Section -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-5 rounded-xl mb-6 border border-blue-200">
            <div class="flex items-center mb-3">
                <div class="bg-blue-500 p-2 rounded-lg mr-3">
                    <i class="material-icons text-white text-sm">help_outline</i>
                </div>
                <h4 class="font-semibold text-blue-900">用户问题</h4>
            </div>
            <p class="text-blue-800 leading-relaxed">${result.question}</p>
        </div>

        <!-- Answer Section -->
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-5 rounded-xl mb-6 border border-green-200">
            <div class="flex items-center mb-3">
                <div class="bg-green-500 p-2 rounded-lg mr-3">
                    <i class="material-icons text-white text-sm">psychology</i>
                </div>
                <h4 class="font-semibold text-green-900">AI 回答</h4>
            </div>
            <div class="text-green-800 leading-relaxed whitespace-pre-wrap">${formattedAnswer}</div>
        </div>

        ${dataSection}

        <!-- Footer Stats -->
        <div class="bg-gray-50 p-4 rounded-xl border border-gray-200 mt-6">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-6">
                    <div class="flex items-center text-gray-600">
                        <i class="material-icons text-sm mr-2">timeline</i>
                        <span class="text-sm">执行时间: ${(result.execution_time || 0).toFixed(2)}秒</span>
                    </div>
                    ${result.data ? `
                        <div class="flex items-center text-gray-600">
                            <i class="material-icons text-sm mr-2">storage</i>
                            <span class="text-sm">${result.data.length} 行数据</span>
                        </div>
                    ` : ''}
                </div>
                <div class="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                    <i class="material-icons text-sm mr-1">check_circle</i>
                    查询完成
                </div>
            </div>
        </div>
    `;

    container.appendChild(resultCard);
}

// Parse complex task answer to extract sub-task results
function parseComplexTaskAnswer(answer) {
    const subTasks = [];

    // Split by sub-task markers
    const sections = answer.split(/\*\*子任务 \d+\*\*:/);

    for (let i = 1; i < sections.length; i++) {
        const section = sections[i].trim();
        const nextTaskIndex = section.indexOf('==================================================');
        const taskContent = nextTaskIndex > 0 ? section.substring(0, nextTaskIndex).trim() : section.trim();

        subTasks.push({
            index: i,
            content: taskContent,
            hasSQL: taskContent.includes('🔍 **执行的SQL查询:**'),
            hasResults: taskContent.includes('📊 **查询结果:**')
        });
    }

    return subTasks;
}

// Format complex task results for display
function formatComplexTaskResults(subTasks) {
    return subTasks.map(task => `
        <div class="mb-6 p-5 bg-gradient-to-br from-white to-gray-50 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full w-10 h-10 flex items-center justify-center text-sm font-bold mr-4 shadow-lg">
                        ${task.index}
                    </div>
                    <div>
                        <h5 class="font-semibold text-gray-800 text-lg">子任务 ${task.index} 执行结果</h5>
                        <div class="flex items-center space-x-2 mt-1">
                            ${task.hasSQL ? '<span class="bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium"><i class="material-icons text-xs mr-1">code</i>SQL已执行</span>' : ''}
                            ${task.hasResults ? '<span class="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium"><i class="material-icons text-xs mr-1">table_chart</i>数据已返回</span>' : ''}
                        </div>
                    </div>
                </div>
                <div class="text-gray-400">
                    <i class="material-icons">expand_more</i>
                </div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div class="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap font-mono">${task.content}</div>
            </div>
        </div>
    `).join('');
}

// Toggle complex results visibility
function toggleComplexResults(index) {
    const resultsDiv = document.getElementById(`complex-results-${index}`);
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (resultsDiv.classList.contains('hidden')) {
        resultsDiv.classList.remove('hidden');
        resultsDiv.style.animation = 'slideDown 0.3s ease-out';
        icon.textContent = 'expand_less';
        button.innerHTML = '<i class="material-icons text-sm mr-1">expand_less</i>收起详情';
        button.className = 'bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-red-600 hover:to-pink-700 transition-all duration-200 shadow-md hover:shadow-lg';
    } else {
        resultsDiv.style.animation = 'slideUp 0.3s ease-out';
        setTimeout(() => {
            resultsDiv.classList.add('hidden');
        }, 300);
        icon.textContent = 'expand_more';
        button.innerHTML = '<i class="material-icons text-sm mr-1">expand_more</i>查看详情';
        button.className = 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg';
    }
}

// Simple download function
function downloadData(index, format) {
    console.log('🔥 Download requested:', index, format);
    alert(`Download ${format} for result ${index} - Feature coming soon!`);
}

console.log('🔥 NEW Utils.js fully loaded and ready!');
