{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AutoGen + ClickHouse Demo Notebook\n", "\n", "This notebook demonstrates the integration of Microsoft AutoGen with ClickHouse database for multi-agent data analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Import our modules\n", "from clickhouse_client import clickhouse_client\n", "from agents import AgentFactory, execute_clickhouse_query, get_database_schema\n", "from config import CLICKHOUSE_CONFIG\n", "\n", "# Configure plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Setup complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Database Connection and Schema"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check database connection\n", "try:\n", "    tables = clickhouse_client.get_tables()\n", "    print(f\"Connected to ClickHouse database: {CLICKHOUSE_CONFIG['database']}\")\n", "    print(f\"Available tables: {tables}\")\n", "except Exception as e:\n", "    print(f\"Database connection failed: {e}\")\n", "    print(\"Please run setup_database.py first\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display database schema\n", "schema = get_database_schema()\n", "print(schema)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sample Data Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Show sample data from each table\n", "tables = clickhouse_client.get_tables()\n", "\n", "for table in tables:\n", "    print(f\"\\n=== {table.upper()} ===\")\n", "    sample_df = clickhouse_client.get_table_sample(table, 5)\n", "    display(sample_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Analysis with Direct Queries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sales by region\n", "query = \"SELECT region, SUM(total_amount) as total_sales FROM sales GROUP BY region ORDER BY total_sales DESC\"\n", "sales_by_region = clickhouse_client.execute_query(query)\n", "\n", "print(\"Sales by Region:\")\n", "display(sales_by_region)\n", "\n", "# Visualize\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(sales_by_region['region'], sales_by_region['total_sales'])\n", "plt.title('Total Sales by Region')\n", "plt.xlabel('Region')\n", "plt.ylabel('Total Sales')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Top products by sales\n", "query = \"\"\"\n", "SELECT p.name, p.category, SUM(s.total_amount) as total_sales \n", "FROM sales s \n", "JOIN products p ON s.product_id = p.id \n", "GROUP BY p.name, p.category \n", "ORDER BY total_sales DESC \n", "LIMIT 10\n", "\"\"\"\n", "\n", "top_products = clickhouse_client.execute_query(query)\n", "\n", "print(\"Top 10 Products by Sales:\")\n", "display(top_products)\n", "\n", "# Visualize\n", "plt.figure(figsize=(12, 8))\n", "plt.barh(top_products['name'], top_products['total_sales'])\n", "plt.title('Top 10 Products by Sales')\n", "plt.xlabel('Total Sales')\n", "plt.ylabel('Product Name')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Monthly sales trend\n", "query = \"\"\"\n", "SELECT \n", "    toYYYYMM(sale_date) as month,\n", "    SUM(total_amount) as monthly_sales,\n", "    COUNT(*) as transaction_count\n", "FROM sales \n", "GROUP BY month \n", "ORDER BY month\n", "\"\"\"\n", "\n", "monthly_trend = clickhouse_client.execute_query(query)\n", "\n", "print(\"Monthly Sales Trend:\")\n", "display(monthly_trend)\n", "\n", "# Convert month to datetime for better plotting\n", "monthly_trend['month_date'] = pd.to_datetime(monthly_trend['month'].astype(str), format='%Y%m')\n", "\n", "# Visualize\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))\n", "\n", "# Sales trend\n", "ax1.plot(monthly_trend['month_date'], monthly_trend['monthly_sales'], marker='o')\n", "ax1.set_title('Monthly Sales Trend')\n", "ax1.set_ylabel('Monthly Sales')\n", "ax1.grid(True)\n", "\n", "# Transaction count trend\n", "ax2.plot(monthly_trend['month_date'], monthly_trend['transaction_count'], marker='s', color='orange')\n", "ax2.set_title('Monthly Transaction Count')\n", "ax2.set_xlabel('Month')\n", "ax2.set_ylabel('Transaction Count')\n", "ax2.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AutoGen Agents Demo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create agents\n", "agents = AgentFactory.create_agents()\n", "\n", "# Override user proxy for notebook mode\n", "import autogen\n", "\n", "agents[\"user_proxy\"] = autogen.UserProxyAgent(\n", "    name=\"UserProxy\",\n", "    human_input_mode=\"NEVER\",\n", "    max_consecutive_auto_reply=5,\n", "    is_termination_msg=lambda x: x.get(\"content\", \"\").rstrip().endswith(\"TERMINATE\"),\n", "    code_execution_config=False,\n", ")\n", "\n", "print(\"Agents created successfully!\")\n", "print(f\"Available agents: {list(agents.keys())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demo 1: Product Analysis\n", "print(\"=== DEMO 1: Product Analysis ===\")\n", "\n", "group_chat = AgentFactory.create_group_chat(agents)\n", "manager = AgentFactory.create_group_chat_manager(group_chat)\n", "\n", "agents[\"user_proxy\"].initiate_chat(\n", "    manager,\n", "    message=\"Analyze the top-performing products by category and provide business insights. TERMINATE when done.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demo 2: Customer Analysis\n", "print(\"\\n=== DEMO 2: Customer Analysis ===\")\n", "\n", "group_chat = AgentFactory.create_group_chat(agents)\n", "manager = AgentFactory.create_group_chat_manager(group_chat)\n", "\n", "agents[\"user_proxy\"].initiate_chat(\n", "    manager,\n", "    message=\"Analyze customer demographics and purchasing patterns. Identify key customer segments. TERMINATE when done.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demo 3: Sales Performance Report\n", "print(\"\\n=== DEMO 3: Sales Performance Report ===\")\n", "\n", "group_chat = AgentFactory.create_group_chat(agents)\n", "manager = AgentFactory.create_group_chat_manager(group_chat)\n", "\n", "agents[\"user_proxy\"].initiate_chat(\n", "    manager,\n", "    message=\"Generate a comprehensive sales performance report including trends, top performers, and recommendations. TERMINATE when done.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom Analysis Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_customer_lifetime_value():\n", "    \"\"\"Calculate customer lifetime value.\"\"\"\n", "    query = \"\"\"\n", "    SELECT \n", "        c.country,\n", "        c.age,\n", "        COUNT(s.id) as total_orders,\n", "        SUM(s.total_amount) as total_spent,\n", "        AVG(s.total_amount) as avg_order_value,\n", "        MAX(s.sale_date) as last_purchase_date\n", "    FROM customers c\n", "    LEFT JOIN sales s ON c.id = s.customer_id\n", "    GROUP BY c.id, c.country, c.age\n", "    HAVING total_orders > 0\n", "    ORDER BY total_spent DESC\n", "    LIMIT 20\n", "    \"\"\"\n", "    \n", "    result = clickhouse_client.execute_query(query)\n", "    return result\n", "\n", "clv_data = analyze_customer_lifetime_value()\n", "print(\"Top 20 Customers by Lifetime Value:\")\n", "display(clv_data)\n", "\n", "# Visualize CLV by country\n", "plt.figure(figsize=(12, 6))\n", "country_clv = clv_data.groupby('country')['total_spent'].mean().sort_values(ascending=False)\n", "plt.bar(country_clv.index, country_clv.values)\n", "plt.title('Average Customer Lifetime Value by Country')\n", "plt.xlabel('Country')\n", "plt.ylabel('Average CLV')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "This notebook demonstrated:\n", "\n", "1. **ClickHouse Integration**: Direct database queries and data retrieval\n", "2. **Data Visualization**: Charts and graphs using matplotlib and seaborn\n", "3. **AutoGen Agents**: Multi-agent system for automated data analysis\n", "4. **Business Intelligence**: Practical analytics for sales and customer data\n", "\n", "The combination of AutoGen and ClickHouse provides a powerful platform for:\n", "- Automated data analysis\n", "- Interactive business intelligence\n", "- Scalable analytics workflows\n", "- Multi-agent collaboration for complex analysis tasks"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}