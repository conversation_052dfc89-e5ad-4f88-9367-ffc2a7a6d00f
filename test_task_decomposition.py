#!/usr/bin/env python3
"""
Test script for task decomposition functionality
"""

import asyncio
import json
import aiohttp
import time

async def test_task_decomposition():
    """Test the task decomposition feature"""
    
    # Test data - using questions that match available database tables
    test_questions = [
        "分析日志数据并显示各个域名的访问情况以及不同HTTP方法的使用统计",
        "显示访问来源IP分布情况和响应状态码分析，同时统计不同时间段的访问模式",
        "比较不同域名的访问频率并分析响应时间分布情况"
    ]
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        for i, question in enumerate(test_questions, 1):
            print(f"\n{'='*60}")
            print(f"测试 {i}: {question}")
            print('='*60)
            
            # Test with task decomposition enabled
            print("\n🔄 启用任务拆解...")
            task_id = await create_ai_query_task(session, base_url, question, enable_decomposition=True)
            
            if task_id:
                print(f"✅ 任务创建成功: {task_id}")
                await monitor_task_progress(session, base_url, task_id)
            else:
                print("❌ 任务创建失败")
            
            print("\n" + "-"*40)
            
            # Test without task decomposition for comparison
            print("\n🔄 不启用任务拆解...")
            task_id_simple = await create_ai_query_task(session, base_url, question, enable_decomposition=False)
            
            if task_id_simple:
                print(f"✅ 简单任务创建成功: {task_id_simple}")
                await monitor_task_progress(session, base_url, task_id_simple)
            else:
                print("❌ 简单任务创建失败")
            
            print("\n" + "="*60)
            await asyncio.sleep(2)  # Wait between tests

async def create_ai_query_task(session, base_url, question, enable_decomposition=True):
    """Create an AI query task"""
    try:
        payload = {
            "question": question,
            "include_raw_data": True,
            "session_id": f"test_session_{int(time.time())}",
            "optimize_prompt": False,
            "enable_task_decomposition": enable_decomposition,
            "generate_charts": False
        }
        
        async with session.post(f"{base_url}/api/tasks/ai-query", json=payload) as response:
            if response.status == 200:
                result = await response.json()
                return result.get("task_id")
            else:
                print(f"❌ HTTP错误: {response.status}")
                error_text = await response.text()
                print(f"错误详情: {error_text}")
                return None
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

async def monitor_task_progress(session, base_url, task_id, max_wait=60):
    """Monitor task progress"""
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            async with session.get(f"{base_url}/api/tasks/{task_id}") as response:
                if response.status == 200:
                    task_data = await response.json()
                    status = task_data.get("status")
                    progress = task_data.get("progress", 0)
                    current_step = task_data.get("current_step_index", 0)
                    steps = task_data.get("steps", [])
                    
                    if steps and current_step < len(steps):
                        current_step_name = steps[current_step].get("name", "unknown")
                        current_step_desc = steps[current_step].get("description", "")
                        print(f"📊 进度: {progress:.1f}% | 状态: {status} | 当前步骤: {current_step_name} - {current_step_desc}")
                    else:
                        print(f"📊 进度: {progress:.1f}% | 状态: {status}")
                    
                    if status in ["completed", "failed", "cancelled"]:
                        print(f"\n🏁 任务完成! 最终状态: {status}")
                        
                        if status == "completed" and task_data.get("result"):
                            result = task_data["result"]
                            print(f"\n📋 任务结果:")
                            print(f"   原始问题: {result.get('question', 'N/A')}")
                            if result.get('processed_question') != result.get('question'):
                                print(f"   处理后问题: {result.get('processed_question', 'N/A')}")
                            print(f"   任务拆解: {'是' if result.get('task_decomposition_used') else '否'}")
                            
                            if result.get('sub_tasks'):
                                print(f"   子任务数量: {len(result['sub_tasks'])}")
                                for i, sub_task in enumerate(result['sub_tasks'], 1):
                                    print(f"     {i}. {sub_task}")
                            
                            answer = result.get('answer', '')
                            if len(answer) > 200:
                                print(f"   回答: {answer[:200]}...")
                            else:
                                print(f"   回答: {answer}")
                                
                        elif status == "failed":
                            error = task_data.get("error", "未知错误")
                            print(f"❌ 任务失败: {error}")
                        
                        break
                        
                else:
                    print(f"❌ 获取任务状态失败: {response.status}")
                    break
                    
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            break
            
        await asyncio.sleep(2)
    else:
        print(f"⏰ 任务监控超时 ({max_wait}秒)")

if __name__ == "__main__":
    print("🚀 开始测试任务拆解功能...")
    asyncio.run(test_task_decomposition())
    print("\n✅ 测试完成!")
