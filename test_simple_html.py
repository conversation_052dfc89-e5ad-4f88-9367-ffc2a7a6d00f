#!/usr/bin/env python3
"""
Test simple HTML file
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def test_simple_html():
    """Test the simple HTML file."""
    
    async with async_playwright() as p:
        print("🧪 Testing simple HTML...")
        
        # Launch browser
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # Enable console logging
        page.on("console", lambda msg: print(f"Console: {msg.text}"))
        page.on("pageerror", lambda error: print(f"Error: {error}"))
        
        try:
            # Get the file path
            file_path = os.path.abspath("test_js_syntax.html")
            file_url = f"file://{file_path}"
            
            print(f"Loading: {file_url}")
            await page.goto(file_url)
            
            # Wait for page to load
            await page.wait_for_selector("h1", timeout=10000)
            print("✅ Page loaded")
            
            # Wait for initialization
            await page.wait_for_timeout(2000)
            
            # Check jQuery
            jquery_loaded = await page.evaluate("() => typeof $ !== 'undefined'")
            print(f"jQuery loaded: {jquery_loaded}")
            
            # Check if our functions exist
            submit_query_exists = await page.evaluate("() => typeof window.submitQuery === 'function'")
            print(f"submitQuery function exists: {submit_query_exists}")
            
            test_function_exists = await page.evaluate("() => typeof window.testFunction === 'function'")
            print(f"testFunction exists: {test_function_exists}")
            
            # Test the function
            if test_function_exists:
                result = await page.evaluate("() => window.testFunction()")
                print(f"Test function result: {result}")
            
            # Test input and button
            await page.fill("#question-input", "test question")
            await page.click("#ask-button")
            
            # Wait and check results
            await page.wait_for_timeout(2000)
            results_text = await page.text_content("#results")
            print(f"Results: {results_text}")
            
            # Keep browser open for inspection
            print("⏳ Keeping browser open for 10 seconds...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"❌ Test error: {e}")
            
        finally:
            await browser.close()
            print("🎭 Browser closed")

if __name__ == "__main__":
    asyncio.run(test_simple_html())
