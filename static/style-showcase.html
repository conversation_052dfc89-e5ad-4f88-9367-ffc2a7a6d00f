<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复杂任务展示样式预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="min-h-screen py-8">
        <div class="container mx-auto px-4">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">🎨 复杂任务展示样式预览</h1>
                <p class="text-white/80 text-lg">全新设计的智能任务分析界面</p>
            </div>
            
            <!-- Control Panel -->
            <div class="glass-effect rounded-2xl p-6 mb-8">
                <div class="flex flex-wrap gap-4 justify-center">
                    <button onclick="showComplexTask()" class="bg-gradient-to-r from-purple-500 to-indigo-600 text-white px-6 py-3 rounded-xl font-medium hover:from-purple-600 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="material-icons mr-2">account_tree</i>复杂任务分析
                    </button>
                    <button onclick="showSimpleTask()" class="bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-cyan-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="material-icons mr-2">psychology</i>简单查询
                    </button>
                    <button onclick="showDataTable()" class="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-xl font-medium hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="material-icons mr-2">table_chart</i>数据表格
                    </button>
                    <button onclick="clearAll()" class="bg-gradient-to-r from-red-500 to-pink-600 text-white px-6 py-3 rounded-xl font-medium hover:from-red-600 hover:to-pink-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="material-icons mr-2">clear</i>清空
                    </button>
                </div>
            </div>

            <!-- Results Container -->
            <div id="results-container" style="display: none;">
                <div id="results-list"></div>
            </div>

            <!-- Welcome Message -->
            <div id="welcome-message" class="text-center">
                <div class="glass-effect rounded-2xl p-12">
                    <i class="material-icons text-6xl text-white/60 mb-4">auto_awesome</i>
                    <h2 class="text-2xl font-bold text-white mb-4">选择一个选项开始预览</h2>
                    <p class="text-white/70">体验全新设计的智能任务分析界面</p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/utils-new.js"></script>
    <script>
        function showComplexTask() {
            const complexResult = {
                question: "分析网站访问日志，统计各域名访问量、HTTP方法分布、响应状态码分析以及访问时间模式",
                task_decomposition_used: true,
                sub_tasks: [
                    "提取并统计所有访问域名的访问次数",
                    "分析各种HTTP方法（GET、POST等）的使用分布",
                    "统计不同响应状态码的出现频率",
                    "分析访问时间模式和高峰期",
                    "生成综合访问分析报告"
                ],
                answer: `**复杂任务智能分析** (拆解为 5 个子任务):

**子任务 1**: 提取并统计所有访问域名的访问次数

🔍 **执行的SQL查询:**
\`\`\`sql
SELECT host, COUNT(*) AS visit_count FROM logs GROUP BY host ORDER BY visit_count DESC LIMIT 10
\`\`\`
📊 **查询结果:** 共 25 行数据，显示前 10 行:
- bypaas.yuanbao.tencent.com: 7,988 次访问
- gateway.misterym.top: 4,692 次访问
- apibenchtest.misterym.top: 4,677 次访问

==================================================

**子任务 2**: 分析各种HTTP方法的使用分布

🔍 **执行的SQL查询:**
\`\`\`sql
SELECT method, COUNT(*) AS method_count FROM logs GROUP BY method ORDER BY method_count DESC
\`\`\`
📊 **查询结果:** 共 3 行数据:
- GET: 28,456 次 (95.2%)
- POST: 1,234 次 (4.1%)
- PUT: 205 次 (0.7%)

==================================================

**子任务 3**: 统计不同响应状态码的出现频率

🔍 **执行的SQL查询:**
\`\`\`sql
SELECT status, COUNT(*) AS status_count FROM logs GROUP BY status ORDER BY status_count DESC
\`\`\`
📊 **查询结果:** 共 5 行数据:
- 200: 26,789 次 (89.5%)
- 404: 1,856 次 (6.2%)
- 301: 892 次 (3.0%)
- 500: 234 次 (0.8%)
- 403: 124 次 (0.4%)

==================================================

**综合分析总结**:

本次分析共执行了 5 个主要分析任务，包含 12 个嵌套子任务。每个任务都提供了具体的数据查询和分析结果，为您的决策提供了全面的数据支持。`,
                execution_time: 23.7,
                timestamp: new Date().toISOString()
            };

            hideWelcome();
            displayResultsNew([complexResult]);
        }

        function showSimpleTask() {
            const simpleResult = {
                question: "查询访问量最高的前10个域名",
                task_decomposition_used: false,
                answer: "🔍 **执行的SQL查询:**\n```sql\nSELECT host, COUNT(*) as visit_count FROM logs GROUP BY host ORDER BY visit_count DESC LIMIT 10\n```\n\n📊 **查询结果:** 共 10 行数据\n\n查询成功执行，返回了访问量最高的10个域名。可以看到bypaas.yuanbao.tencent.com是访问量最高的域名，其次是gateway.misterym.top。这些数据可以帮助了解网站的主要流量来源。",
                data: [
                    { host: "bypaas.yuanbao.tencent.com", visit_count: 7988, percentage: "26.7%" },
                    { host: "gateway.misterym.top", visit_count: 4692, percentage: "15.7%" },
                    { host: "apibenchtest.misterym.top", visit_count: 4677, percentage: "15.6%" },
                    { host: "blog.misterym.top", visit_count: 2385, percentage: "8.0%" },
                    { host: "bot.misterym.top", visit_count: 2358, percentage: "7.9%" },
                    { host: "*************", visit_count: 2655, percentage: "8.9%" },
                    { host: "b2j3.com", visit_count: 514, percentage: "1.7%" },
                    { host: "ch.misterym.top", visit_count: 450, percentage: "1.5%" },
                    { host: "1panel.misterym.top", visit_count: 348, percentage: "1.2%" },
                    { host: "bbct.misterym.top", visit_count: 31, percentage: "0.1%" }
                ],
                execution_time: 1.8,
                timestamp: new Date().toISOString()
            };

            hideWelcome();
            displayResultsNew([simpleResult]);
        }

        function showDataTable() {
            const tableResult = {
                question: "展示数据表格样式效果",
                task_decomposition_used: false,
                answer: "这是一个数据表格样式展示，包含了渐变边框、悬停效果、响应式设计等现代化特性。",
                data: [
                    { id: 1, name: "张三", email: "<EMAIL>", role: "管理员", status: "活跃", last_login: "2024-01-15 10:30:00" },
                    { id: 2, name: "李四", email: "<EMAIL>", role: "用户", status: "活跃", last_login: "2024-01-15 09:15:00" },
                    { id: 3, name: "王五", email: "<EMAIL>", role: "编辑", status: "离线", last_login: "2024-01-14 16:45:00" },
                    { id: 4, name: "赵六", email: "<EMAIL>", role: "用户", status: "活跃", last_login: "2024-01-15 11:20:00" },
                    { id: 5, name: "钱七", email: "<EMAIL>", role: "管理员", status: "活跃", last_login: "2024-01-15 08:30:00" }
                ],
                execution_time: 0.5,
                timestamp: new Date().toISOString()
            };

            hideWelcome();
            displayResultsNew([tableResult]);
        }

        function hideWelcome() {
            document.getElementById('welcome-message').style.display = 'none';
            document.getElementById('results-container').style.display = 'block';
        }

        function clearAll() {
            document.getElementById('results-container').style.display = 'none';
            document.getElementById('welcome-message').style.display = 'block';
            document.getElementById('results-list').innerHTML = '';
        }
    </script>
</body>
</html>
