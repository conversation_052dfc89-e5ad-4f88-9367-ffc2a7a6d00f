<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .log {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
        .info {
            color: #74c0fc;
        }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    
    <div>
        <button onclick="connectWebSocket()">Connect WebSocket</button>
        <button onclick="sendPing()">Send Ping</button>
        <button onclick="getTasks()">Get Tasks</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="status">Status: Disconnected</div>
    <div id="log" class="log"></div>

    <script>
        let websocket = null;
        const clientId = 'test_client_' + Date.now();
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status) {
            document.getElementById('status').textContent = `Status: ${status}`;
        }
        
        function connectWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/${clientId}`;
                
                log(`Attempting to connect to: ${wsUrl}`, 'info');
                updateStatus('Connecting...');
                
                websocket = new WebSocket(wsUrl);
                
                websocket.onopen = function(event) {
                    log('✅ WebSocket connected successfully!', 'success');
                    updateStatus('Connected');
                };
                
                websocket.onmessage = function(event) {
                    log(`📨 Received: ${event.data}`, 'success');
                };
                
                websocket.onclose = function(event) {
                    log(`🔌 WebSocket closed: Code ${event.code}, Reason: ${event.reason}`, 'error');
                    updateStatus('Disconnected');
                };
                
                websocket.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus('Error');
                };
                
            } catch (error) {
                log(`❌ Connection error: ${error}`, 'error');
                updateStatus('Error');
            }
        }
        
        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = JSON.stringify({ type: 'ping' });
                websocket.send(message);
                log(`📤 Sent: ${message}`, 'info');
            } else {
                log('❌ WebSocket not connected', 'error');
            }
        }
        
        function getTasks() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = JSON.stringify({ type: 'get_tasks' });
                websocket.send(message);
                log(`📤 Sent: ${message}`, 'info');
            } else {
                log('❌ WebSocket not connected', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('🚀 WebSocket test page loaded', 'info');
            log(`Client ID: ${clientId}`, 'info');
        };
    </script>
</body>
</html>
