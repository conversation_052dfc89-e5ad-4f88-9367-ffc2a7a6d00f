// Modern Async Task Management System with WebSocket
class TaskManager {
    constructor() {
        this.tasks = new Map();
        this.listeners = new Map();
        this.clientId = 'client_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        // Task status constants
        this.STATUS = {
            PENDING: 'pending',
            PLANNING: 'planning',
            EXECUTING: 'executing',
            COMPLETED: 'completed',
            FAILED: 'failed',
            CANCELLED: 'cancelled'
        };

        this.initializeUI();
        this.connectWebSocket();
    }

    // WebSocket connection management
    connectWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/${this.clientId}`;

            console.log('Connecting to WebSocket:', wsUrl);
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('✅ WebSocket connected successfully');
                this.reconnectAttempts = 0;

                // Request current tasks
                this.sendMessage({
                    type: 'get_tasks'
                });
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    console.log('📨 WebSocket message received:', message);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    console.error('❌ Error parsing WebSocket message:', error);
                }
            };

            this.websocket.onclose = (event) => {
                console.log('🔌 WebSocket disconnected:', event.code, event.reason);
                this.attemptReconnect();
            };

            this.websocket.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
            };

        } catch (error) {
            console.error('❌ Error connecting WebSocket:', error);
            this.attemptReconnect();
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

            setTimeout(() => {
                this.connectWebSocket();
            }, 2000 * this.reconnectAttempts); // Exponential backoff
        } else {
            console.error('Max reconnection attempts reached');
        }
    }

    sendMessage(message) {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify(message));
        }
    }

    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'task_update':
                this.handleTaskUpdate(message.data);
                break;
            case 'tasks_list':
                this.handleTasksList(message.data);
                break;
            case 'pong':
                // Keep-alive response
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    handleTaskUpdate(taskData) {
        console.log('📨 Handling task update:', taskData);
        const task = this.convertServerTask(taskData);
        console.log('🔄 Converted task:', task);

        this.tasks.set(task.id, task);
        this.updateUI();
        this.emit('taskUpdated', task);

        // Emit to global app if available
        if (window.app && typeof window.app.updateAnalyticsTab === 'function') {
            window.app.updateAnalyticsTab();
        }

        // Emit specific status events
        if (task.status === this.STATUS.COMPLETED) {
            console.log('🎉 Task completed, emitting event:', task);
            this.emit('taskCompleted', task);
        } else if (task.status === this.STATUS.FAILED) {
            console.log('❌ Task failed, emitting event:', task);
            this.emit('taskFailed', task);
        }
    }

    handleTasksList(tasksData) {
        this.tasks.clear();
        tasksData.forEach(taskData => {
            const task = this.convertServerTask(taskData);
            this.tasks.set(task.id, task);
        });
        this.updateUI();
    }

    convertServerTask(serverTask) {
        return {
            id: serverTask.task_id,
            name: serverTask.name,
            type: serverTask.task_type,
            status: serverTask.status,
            progress: serverTask.progress,
            steps: serverTask.steps || [],
            currentStepIndex: serverTask.current_step_index || 0,
            startTime: serverTask.start_time ? new Date(serverTask.start_time) : null,
            endTime: serverTask.end_time ? new Date(serverTask.end_time) : null,
            createdAt: new Date(serverTask.created_at),
            result: serverTask.result,
            error: serverTask.error,
            metadata: serverTask.metadata || {}
        };
    }

    // Create AI query task
    async createAIQueryTask(question, options = {}) {
        try {
            const response = await fetch('/api/tasks/ai-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: question,
                    include_raw_data: options.includeRawData || false,
                    session_id: options.sessionId || null,
                    optimize_prompt: options.optimizePrompt || false,
                    enable_task_decomposition: options.enableTaskDecomposition || false,
                    generate_charts: options.generateCharts || false
                })
            });

            if (!response.ok) {
                throw new Error(`Failed to create task: ${response.status}`);
            }

            const result = await response.json();
            return result.task_id;

        } catch (error) {
            console.error('Error creating AI query task:', error);
            throw error;
        }
    }

    // Create SQL execution task
    async createSQLExecuteTask(sql, exportFormat = null) {
        try {
            const response = await fetch('/api/tasks/sql-execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sql: sql,
                    export_format: exportFormat
                })
            });

            if (!response.ok) {
                throw new Error(`Failed to create task: ${response.status}`);
            }

            const result = await response.json();
            return result.task_id;

        } catch (error) {
            console.error('Error creating SQL execute task:', error);
            throw error;
        }
    }

    // Cancel a task
    async cancelTask(taskId) {
        try {
            const response = await fetch(`/api/tasks/${taskId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`Failed to cancel task: ${response.status}`);
            }

            const result = await response.json();
            return result.message;

        } catch (error) {
            console.error('Error cancelling task:', error);
            throw error;
        }
    }

    // Clear completed tasks
    async clearCompleted() {
        try {
            const response = await fetch('/api/tasks', {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`Failed to clear tasks: ${response.status}`);
            }

            const result = await response.json();

            // Remove completed tasks from local storage
            const completedTasks = Array.from(this.tasks.values()).filter(task =>
                task.status === this.STATUS.COMPLETED ||
                task.status === this.STATUS.FAILED ||
                task.status === this.STATUS.CANCELLED
            );

            completedTasks.forEach(task => {
                this.tasks.delete(task.id);
            });

            this.updateUI();
            this.emit('tasksCleared', completedTasks);

            return result.message;

        } catch (error) {
            console.error('Error clearing completed tasks:', error);
            throw error;
        }
    }

    // Old cancelTask method removed - using the async API version above

    // Get task by ID
    getTask(taskId) {
        return this.tasks.get(taskId);
    }

    // Get all tasks
    getAllTasks() {
        return Array.from(this.tasks.values());
    }

    // Get tasks by status
    getTasksByStatus(status) {
        return this.getAllTasks().filter(task => task.status === status);
    }

    // Clear completed tasks
    clearCompleted() {
        const completedTasks = this.getTasksByStatus(this.STATUS.COMPLETED);
        completedTasks.forEach(task => {
            this.tasks.delete(task.id);
        });
        this.updateUI();
        this.emit('tasksCleared', completedTasks);
    }

    // Event system
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index !== -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in task manager event listener:', error);
                }
            });
        }
    }

    // Initialize UI components
    initializeUI() {
        this.createTaskPanel();
    }

    // Create task management panel
    createTaskPanel() {
        // Check if panel already exists
        if (document.getElementById('task-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'task-panel';
        panel.className = 'fixed bottom-6 right-6 w-80 max-h-96 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl z-50 overflow-hidden';
        panel.style.display = 'none';

        panel.innerHTML = `
            <div class="p-4 border-b border-white/20">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i data-lucide="activity" class="w-5 h-5 text-white mr-2"></i>
                        <h3 class="text-white font-semibold">Tasks</h3>
                        <span id="task-count" class="ml-2 px-2 py-1 bg-white/20 rounded-full text-xs text-white">0</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="clear-completed" class="text-white/60 hover:text-white text-xs">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                        <button id="toggle-task-panel" class="text-white/60 hover:text-white">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div id="task-list" class="max-h-80 overflow-y-auto custom-scrollbar">
                <!-- Tasks will be populated here -->
            </div>
        `;

        document.body.appendChild(panel);

        // Add event listeners
        document.getElementById('toggle-task-panel').addEventListener('click', () => {
            this.hideTaskPanel();
        });

        document.getElementById('clear-completed').addEventListener('click', () => {
            this.clearCompleted();
        });

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Show task panel
    showTaskPanel() {
        const panel = document.getElementById('task-panel');
        if (panel) {
            panel.style.display = 'block';
            panel.classList.add('fade-in');
        }
    }

    // Hide task panel
    hideTaskPanel() {
        const panel = document.getElementById('task-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    // Update UI
    updateUI() {
        this.updateTaskCount();
        this.updateTaskList();
        this.updateTaskIndicator();
    }

    // Update task count
    updateTaskCount() {
        const countElement = document.getElementById('task-count');
        if (countElement) {
            // Count active tasks (pending, planning, executing)
            const activeTasks = Array.from(this.tasks.values()).filter(task =>
                task.status === this.STATUS.PENDING ||
                task.status === this.STATUS.PLANNING ||
                task.status === this.STATUS.EXECUTING
            );

            const totalActive = activeTasks.length;

            countElement.textContent = totalActive;
            countElement.className = totalActive > 0
                ? 'ml-2 px-2 py-1 bg-blue-500 rounded-full text-xs text-white animate-pulse'
                : 'ml-2 px-2 py-1 bg-white/20 rounded-full text-xs text-white';
        }
    }

    // Update task list
    updateTaskList() {
        const listElement = document.getElementById('task-list');
        if (!listElement) return;

        const tasks = this.getAllTasks()
            .sort((a, b) => (b.startTime || b.id) - (a.startTime || a.id))
            .slice(0, 10); // Show last 10 tasks

        listElement.innerHTML = tasks.map(task => this.createTaskItem(task)).join('');

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    // Create task item HTML with step details
    createTaskItem(task) {
        const statusIcon = this.getStatusIcon(task.status);
        const statusColor = this.getStatusColor(task.status);
        const duration = task.endTime && task.startTime ?
            `${((new Date(task.endTime) - new Date(task.startTime)) / 1000).toFixed(1)}s` : '';

        // Current step info
        let currentStepInfo = '';
        if (task.steps && task.steps.length > 0) {
            const currentStep = task.steps[task.currentStepIndex];
            if (currentStep) {
                const stepStatusIcon = this.getStatusIcon(currentStep.status);
                const stepStatusColor = this.getStatusColor(currentStep.status);

                currentStepInfo = `
                    <div class="mt-2 p-2 bg-white/5 rounded-lg">
                        <div class="flex items-center justify-between mb-1">
                            <div class="flex items-center">
                                <i data-lucide="${stepStatusIcon}" class="w-3 h-3 ${stepStatusColor} mr-1"></i>
                                <span class="text-white/80 text-xs font-medium">${currentStep.name}</span>
                            </div>
                            <span class="text-white/60 text-xs">${task.currentStepIndex + 1}/${task.steps.length}</span>
                        </div>
                        <p class="text-white/60 text-xs">${currentStep.description}</p>
                        ${currentStep.status === 'executing' ? `
                            <div class="w-full bg-white/20 rounded-full h-1 mt-1">
                                <div class="bg-blue-400 h-1 rounded-full transition-all duration-300 animate-pulse" style="width: 60%"></div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }
        }

        // Progress bar for overall task
        let progressBar = '';
        if (task.status === this.STATUS.EXECUTING || task.status === this.STATUS.PLANNING) {
            progressBar = `
                <div class="w-full bg-white/20 rounded-full h-2 mb-2">
                    <div class="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: ${task.progress}%"></div>
                </div>
                <p class="text-white/70 text-xs mb-1">${Math.round(task.progress)}% complete</p>
            `;
        }

        return `
            <div class="p-4 border-b border-white/10 last:border-b-0 hover:bg-white/5 transition-colors">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <i data-lucide="${statusIcon}" class="w-4 h-4 ${statusColor} mr-2"></i>
                        <span class="text-white text-sm font-medium">${task.name}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        ${duration ? `<span class="text-white/60 text-xs">${duration}</span>` : ''}
                        ${task.status === this.STATUS.PENDING || task.status === this.STATUS.PLANNING || task.status === this.STATUS.EXECUTING ?
                            `<button onclick="taskManager.cancelTask('${task.id}')" class="text-red-400 hover:text-red-300 transition-colors">
                                <i data-lucide="x" class="w-3 h-3"></i>
                            </button>` : ''
                        }
                    </div>
                </div>

                ${progressBar}
                ${currentStepInfo}

                ${task.error ? `
                    <div class="mt-2 p-2 bg-red-500/20 border border-red-500/30 rounded-lg">
                        <p class="text-red-300 text-xs">${task.error}</p>
                    </div>
                ` : ''}

                ${task.status === this.STATUS.COMPLETED && task.result ? `
                    <div class="mt-2 p-2 bg-green-500/20 border border-green-500/30 rounded-lg">
                        <p class="text-green-300 text-xs">✅ Task completed successfully</p>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // Get status icon
    getStatusIcon(status) {
        const icons = {
            [this.STATUS.PENDING]: 'clock',
            [this.STATUS.PLANNING]: 'brain',
            [this.STATUS.EXECUTING]: 'loader',
            [this.STATUS.COMPLETED]: 'check-circle',
            [this.STATUS.FAILED]: 'x-circle',
            [this.STATUS.CANCELLED]: 'minus-circle'
        };
        return icons[status] || 'help-circle';
    }

    // Get status color
    getStatusColor(status) {
        const colors = {
            [this.STATUS.PENDING]: 'text-yellow-400',
            [this.STATUS.PLANNING]: 'text-purple-400',
            [this.STATUS.EXECUTING]: 'text-blue-400',
            [this.STATUS.COMPLETED]: 'text-green-400',
            [this.STATUS.FAILED]: 'text-red-400',
            [this.STATUS.CANCELLED]: 'text-gray-400'
        };
        return colors[status] || 'text-gray-400';
    }

    // Update task indicator (floating button)
    updateTaskIndicator() {
        let indicator = document.getElementById('task-indicator');

        // Count active tasks
        const activeTasks = Array.from(this.tasks.values()).filter(task =>
            task.status === this.STATUS.PENDING ||
            task.status === this.STATUS.PLANNING ||
            task.status === this.STATUS.EXECUTING
        );

        const hasActiveTasks = activeTasks.length > 0;

        if (hasActiveTasks && !indicator) {
            // Create floating task indicator
            indicator = document.createElement('button');
            indicator.id = 'task-indicator';
            indicator.className = 'fixed bottom-6 left-6 w-12 h-12 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center shadow-lg z-40 transition-all duration-300';
            indicator.innerHTML = `
                <i data-lucide="activity" class="w-6 h-6 text-white"></i>
                <div class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-xs font-bold">${activeTasks.length}</span>
                </div>
            `;

            indicator.addEventListener('click', () => {
                this.showTaskPanel();
            });

            document.body.appendChild(indicator);

            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        } else if (!hasActiveTasks && indicator) {
            // Remove indicator when no active tasks
            indicator.remove();
        } else if (indicator) {
            // Update count
            const countElement = indicator.querySelector('span');
            if (countElement) {
                countElement.textContent = activeTasks.length;
            }
        }
    }
}

// Create global task manager instance
window.taskManager = new TaskManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskManager;
}
