#!/usr/bin/env python3
"""
Basic test for new AutoGen API with ClickHouse.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from clickhouse_client import clickhouse_client
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install -U 'autogen-agentchat' 'autogen-ext[openai]'")
    sys.exit(1)

def check_database():
    """Check database connection."""
    try:
        tables = clickhouse_client.get_tables()
        print(f"✅ Database connected. Tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def get_sample_data():
    """Get some sample data from database."""
    try:
        # Get sales summary
        query = "SELECT region, COUNT(*) as sales_count, SUM(total_amount) as total_sales FROM sales GROUP BY region ORDER BY total_sales DESC LIMIT 3"
        result = clickhouse_client.execute_query(query)
        return result.to_string(index=False)
    except Exception as e:
        return f"Error getting data: {e}"

async def test_basic_agent():
    """Test basic agent functionality."""
    print("🧪 Testing Basic Agent")
    print("-" * 30)
    
    # Get API configuration
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    model = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
    
    print(f"API Key: {api_key[:10]}..." if api_key else "No API key")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    
    try:
        # Create model client
        model_client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key,
            base_url=base_url if base_url != "https://api.openai.com/v1" else None
        )
        
        # Create agent
        agent = AssistantAgent(
            name="TestAgent",
            model_client=model_client,
            system_message="You are a helpful assistant. Respond briefly and clearly."
        )
        
        # Test simple interaction
        print("\n🤖 Testing agent response...")
        response = await agent.run(task="Hello! Please respond with 'AutoGen test successful!'")
        
        print(f"✅ Agent response: {response.messages[-1].content}")
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        return False

async def test_data_analysis():
    """Test data analysis with agent."""
    print("\n📊 Testing Data Analysis")
    print("-" * 30)
    
    if not check_database():
        return False
    
    # Get sample data
    sample_data = get_sample_data()
    print(f"Sample data:\n{sample_data}")
    
    try:
        # Create model client
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        model = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
        
        model_client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key,
            base_url=base_url if base_url != "https://api.openai.com/v1" else None
        )
        
        # Create analyst agent
        analyst = AssistantAgent(
            name="DataAnalyst",
            model_client=model_client,
            system_message="You are a data analyst. Analyze the provided data and give brief insights."
        )
        
        # Analyze data
        analysis_task = f"""
        Please analyze this sales data and provide 2-3 key insights:
        
        {sample_data}
        
        Focus on regional performance and trends.
        """
        
        print("\n🔍 Analyzing data...")
        response = await analyst.run(task=analysis_task)
        
        print(f"💡 Analysis: {response.messages[-1].content}")
        return True
        
    except Exception as e:
        print(f"❌ Data analysis failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 AutoGen + ClickHouse Basic Test")
    print("=" * 40)
    
    # Test 1: Basic agent
    success1 = await test_basic_agent()
    
    # Test 2: Data analysis
    success2 = await test_data_analysis()
    
    print("\n" + "=" * 40)
    print("📋 Test Results:")
    print(f"Basic Agent: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"Data Analysis: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 All tests passed! AutoGen + ClickHouse integration is working.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check your configuration.")
        return 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\nTest interrupted.")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Test error: {e}")
        sys.exit(1)
