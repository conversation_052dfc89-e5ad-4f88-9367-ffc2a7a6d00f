#!/usr/bin/env python3
"""
Complete AutoGen + ClickHouse Demo
Works with AutoGen 0.6+ and supports OpenAI-compatible APIs
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check LLM provider and required environment variables
llm_provider = os.getenv('LLM_PROVIDER', 'openai').lower()

if llm_provider == 'openai':
    required_env_vars = ['OPENAI_API_KEY']
else:
    print(f"❌ Unsupported LLM provider: {llm_provider}")
    sys.exit(1)

missing_vars = [var for var in required_env_vars if not os.getenv(var)]

if missing_vars:
    print(f"❌ Missing required environment variables for {llm_provider}: {missing_vars}")
    print("Please set them in your .env file")
    sys.exit(1)

print(f"✅ Using LLM provider: {llm_provider}")

try:
    from clickhouse_client import clickhouse_client
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_core.models import ModelInfo
    from config import LLM_CONFIG
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install -U 'autogen-agentchat' 'autogen-ext[openai]'")
    sys.exit(1)

def check_database_connection():
    """Check if database is accessible."""
    try:
        tables = clickhouse_client.get_tables()
        if not tables:
            print("⚠️  No tables found. Please run setup_database.py first.")
            return False
        print(f"✅ Connected to database. Found tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Please ensure ClickHouse is running and configured correctly.")
        return False

def get_model_client():
    """Get the model client with proper model_info for non-OpenAI models."""
    model_name = LLM_CONFIG["model"]
    
    # Check if it's a standard OpenAI model
    standard_openai_models = [
        "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]
    
    if model_name in standard_openai_models:
        # Standard OpenAI model - no model_info needed
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url")
        )
    else:
        # Non-standard model - need to provide model_info
        model_info = ModelInfo(
            family="openai",  # Required field
            vision=False,
            function_calling=True,
            json_output=True,
            structured_output=False  # Add this to avoid warnings
        )
        
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            model_info=model_info
        )

def get_database_info():
    """Get database schema and sample data."""
    try:
        tables = clickhouse_client.get_tables()
        info = "Database Information:\n\n"
        
        for table in tables:
            info += f"Table: {table}\n"
            schema = clickhouse_client.get_table_schema(table)
            for column in schema:
                info += f"  - {column['name']}: {column['type']}\n"
            
            # Add sample data
            sample = clickhouse_client.get_table_sample(table, 2)
            info += f"\nSample data:\n{sample.to_string(index=False)}\n\n"
        
        return info
    except Exception as e:
        return f"Error getting database info: {e}"

async def create_data_agent():
    """Create a data agent specialized in database queries."""
    model_client = get_model_client()
    
    system_message = f"""
    You are a Data Agent specialized in ClickHouse database operations.
    
    Database Information:
    {get_database_info()}
    
    Your responsibilities:
    1. Answer questions about the database structure
    2. Suggest appropriate SQL queries for analysis
    3. Explain data relationships and insights
    4. Provide business-relevant interpretations of data
    
    When users ask about data, provide helpful analysis based on the available tables:
    - sales: transaction data with product_id, customer_id, amounts, regions
    - products: product information with categories and prices  
    - customers: customer demographics with countries and ages
    """
    
    return AssistantAgent(
        name="DataAgent",
        model_client=model_client,
        system_message=system_message
    )

async def create_analyst_agent():
    """Create an analyst agent for business insights."""
    model_client = get_model_client()
    
    system_message = """
    You are a Business Analyst Agent specialized in data analysis and insights.
    
    Your responsibilities:
    1. Analyze data and identify trends, patterns, and anomalies
    2. Provide actionable business insights and recommendations
    3. Create summaries and reports in business-friendly language
    4. Suggest follow-up questions or deeper analysis
    
    Focus on providing valuable insights rather than just describing data.
    Always relate findings back to business impact and opportunities.
    """
    
    return AssistantAgent(
        name="AnalystAgent", 
        model_client=model_client,
        system_message=system_message
    )

async def run_single_agent_demo():
    """Run demo with a single data agent."""
    print("\n" + "="*60)
    print("SINGLE AGENT DEMO")
    print("="*60)
    
    agent = await create_data_agent()
    
    questions = [
        "What kind of data do we have in our database?",
        "What are the key business metrics we can analyze?",
        "How would you analyze sales performance by region?"
    ]
    
    for question in questions:
        print(f"\n🔍 Question: {question}")
        try:
            response = await agent.run(task=question)
            print(f"💬 Response: {response.messages[-1].content}")
        except Exception as e:
            print(f"❌ Error: {e}")
        print("-" * 50)

async def run_multi_agent_demo():
    """Run demo with multiple agents collaborating."""
    print("\n" + "="*60)
    print("MULTI-AGENT COLLABORATION DEMO")
    print("="*60)
    
    # Create agents
    data_agent = await create_data_agent()
    analyst_agent = await create_analyst_agent()
    
    # Create team
    team = RoundRobinGroupChat(participants=[data_agent, analyst_agent])
    
    task = """
    Please analyze our business performance:
    1. Explain what data we have available
    2. Identify the top-performing regions and products
    3. Provide strategic business recommendations
    
    Work together to provide a comprehensive analysis.
    """
    
    print(f"🤖 Task: {task}")
    print("\n🔄 Agents collaborating...")
    
    try:
        response = await team.run(task=task)
        
        print("\n📊 Collaboration Results:")
        for i, message in enumerate(response.messages):
            print(f"\n{i+1}. {message.source}: {message.content}")
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ Collaboration failed: {e}")

async def run_data_analysis_demo():
    """Run demo with actual database analysis."""
    print("\n" + "="*60)
    print("REAL DATA ANALYSIS DEMO")
    print("="*60)
    
    try:
        # Get actual data
        sales_by_region = clickhouse_client.execute_query(
            "SELECT region, COUNT(*) as sales_count, SUM(total_amount) as total_sales FROM sales GROUP BY region ORDER BY total_sales DESC"
        )
        
        top_products = clickhouse_client.execute_query(
            """
            SELECT p.name, p.category, COUNT(s.id) as sales_count, SUM(s.total_amount) as total_sales 
            FROM sales s 
            JOIN products p ON s.product_id = p.id 
            GROUP BY p.name, p.category 
            ORDER BY total_sales DESC 
            LIMIT 5
            """
        )
        
        print("📊 Sales by Region:")
        print(sales_by_region.to_string(index=False))
        print("\n📈 Top 5 Products:")
        print(top_products.to_string(index=False))
        
        # Analyze with agent
        analyst = await create_analyst_agent()
        
        analysis_prompt = f"""
        Analyze this business data and provide strategic insights:
        
        Sales by Region:
        {sales_by_region.to_string(index=False)}
        
        Top 5 Products:
        {top_products.to_string(index=False)}
        
        What are the key insights and recommendations?
        """
        
        print(f"\n🤖 Getting AI analysis...")
        response = await analyst.run(task=analysis_prompt)
        print(f"\n💡 AI Analysis:\n{response.messages[-1].content}")
        
    except Exception as e:
        print(f"❌ Data analysis failed: {e}")

async def main():
    """Main demo function."""
    print("🚀 AutoGen + ClickHouse Complete Demo")
    print("=" * 60)
    print("This demo showcases AI agents analyzing ClickHouse data")
    print("=" * 60)
    
    # Check database connection
    if not check_database_connection():
        return 1
    
    # Show menu
    while True:
        print("\nSelect a demo:")
        print("1. Single Agent Demo")
        print("2. Multi-Agent Collaboration Demo") 
        print("3. Real Data Analysis Demo")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            await run_single_agent_demo()
        elif choice == "2":
            await run_multi_agent_demo()
        elif choice == "3":
            await run_data_analysis_demo()
        elif choice == "4":
            print("Goodbye! 👋")
            break
        else:
            print("❌ Invalid choice. Please try again.")
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\nDemo interrupted. Goodbye! 👋")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Demo error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        try:
            clickhouse_client.close()
        except:
            pass
