#!/usr/bin/env python3
"""
Simple example using new AutoGen API (0.6+) with ClickHouse.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check LLM provider and required environment variables
llm_provider = os.getenv('LLM_PROVIDER', 'openai').lower()

if llm_provider == 'openai':
    required_env_vars = ['OPENAI_API_KEY']
else:
    print(f"❌ Unsupported LLM provider: {llm_provider}")
    sys.exit(1)

missing_vars = [var for var in required_env_vars if not os.getenv(var)]

if missing_vars:
    print(f"❌ Missing required environment variables for {llm_provider}: {missing_vars}")
    print("Please set them in your .env file")
    sys.exit(1)

print(f"✅ Using LLM provider: {llm_provider}")

try:
    from clickhouse_client import clickhouse_client
    from autogen_agentchat.agents import AssistantAgent
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_agentchat.base import Response
    from autogen_agentchat.messages import TextMessage
    from config import LLM_CONFIG
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install -r requirements.txt")
    sys.exit(1)

def check_database_connection():
    """Check if database is accessible."""
    try:
        tables = clickhouse_client.get_tables()
        if not tables:
            print("⚠️  No tables found. Please run setup_database.py first.")
            return False
        print(f"✅ Connected to database. Found tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Please ensure ClickHouse is running and configured correctly.")
        return False

def get_model_client():
    """Get the model client."""
    from autogen_core.models import ModelInfo

    model_name = LLM_CONFIG["model"]

    # Check if it's a standard OpenAI model
    standard_openai_models = [
        "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]

    if model_name in standard_openai_models:
        # Standard OpenAI model - no model_info needed
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url")
        )
    else:
        # Non-standard model - need to provide model_info
        model_info = ModelInfo(
            family="openai",  # Required field
            vision=False,
            function_calling=True,
            json_output=True
        )

        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            model_info=model_info
        )

async def simple_query_example():
    """Demonstrate simple database queries."""
    print("\n" + "="*50)
    print("SIMPLE QUERY EXAMPLE")
    print("="*50)
    
    # Create model client
    model_client = get_model_client()
    
    # Create a data agent
    data_agent = AssistantAgent(
        name="DataAgent",
        model_client=model_client,
        system_message="""
        You are a helpful data analyst. You can help analyze ClickHouse database data.
        
        Available database tables:
        - sales: Sales transaction data (id, product_id, customer_id, sale_date, quantity, unit_price, total_amount, region)
        - products: Product information (id, name, category, price, created_date)
        - customers: Customer demographics (id, name, email, registration_date, country, age)
        
        When asked about data, provide helpful analysis and insights.
        """
    )
    
    # Example queries
    queries = [
        "What kind of data do we have in our database?",
        "Can you help me understand the sales data structure?",
        "What insights can you provide about our business data?"
    ]
    
    for query in queries:
        print(f"\n🔍 Query: {query}")
        try:
            response = await data_agent.run(task=query)
            print(f"💬 Response: {response.messages[-1].content}")
        except Exception as e:
            print(f"❌ Error: {e}")
        print("-" * 40)

async def database_analysis_example():
    """Demonstrate database analysis with actual data."""
    print("\n" + "="*50)
    print("DATABASE ANALYSIS EXAMPLE")
    print("="*50)
    
    # Get some actual data from the database
    try:
        # Get sales by region
        sales_by_region = clickhouse_client.execute_query(
            "SELECT region, SUM(total_amount) as total_sales FROM sales GROUP BY region ORDER BY total_sales DESC"
        )
        
        # Get top products
        top_products = clickhouse_client.execute_query(
            """
            SELECT p.name, p.category, SUM(s.total_amount) as total_sales 
            FROM sales s 
            JOIN products p ON s.product_id = p.id 
            GROUP BY p.name, p.category 
            ORDER BY total_sales DESC 
            LIMIT 5
            """
        )
        
        print("📊 Sales by Region:")
        print(sales_by_region.to_string(index=False))
        print("\n📈 Top 5 Products:")
        print(top_products.to_string(index=False))
        
        # Create analyst agent
        model_client = get_model_client()
        analyst_agent = AssistantAgent(
            name="AnalystAgent",
            model_client=model_client,
            system_message="""
            You are a business analyst. Analyze the provided data and give actionable insights.
            Focus on trends, patterns, and business recommendations.
            """
        )
        
        # Prepare data for analysis
        analysis_prompt = f"""
        Please analyze this business data and provide insights:
        
        Sales by Region:
        {sales_by_region.to_string(index=False)}
        
        Top 5 Products by Sales:
        {top_products.to_string(index=False)}
        
        What insights and recommendations can you provide?
        """
        
        print(f"\n🤖 Asking analyst for insights...")
        response = await analyst_agent.run(task=analysis_prompt)
        print(f"💡 Analysis: {response.messages[-1].content}")
        
    except Exception as e:
        print(f"❌ Database analysis failed: {e}")

async def interactive_chat():
    """Interactive chat with the agent."""
    print("\n" + "="*50)
    print("INTERACTIVE CHAT")
    print("="*50)
    print("Type 'quit' to exit")
    
    # Create agent
    model_client = get_model_client()
    agent = AssistantAgent(
        name="Assistant",
        model_client=model_client,
        system_message="""
        You are a helpful assistant that can help with data analysis questions.
        You have access to a ClickHouse database with sales, products, and customers data.
        Provide helpful and informative responses.
        """
    )
    
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            print("🤖 Thinking...")
            response = await agent.run(task=user_input)
            print(f"🤖 Assistant: {response.messages[-1].content}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

async def main():
    """Main function."""
    print("🚀 AutoGen + ClickHouse Demo (New API)")
    print("=" * 50)
    
    # Check database connection
    if not check_database_connection():
        return 1
    
    # Show menu
    while True:
        print("\nSelect an example:")
        print("1. Simple Query Example")
        print("2. Database Analysis Example")
        print("3. Interactive Chat")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            await simple_query_example()
        elif choice == "2":
            await database_analysis_example()
        elif choice == "3":
            await interactive_chat()
        elif choice == "4":
            print("Goodbye! 👋")
            break
        else:
            print("❌ Invalid choice. Please try again.")
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\nExiting...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        try:
            clickhouse_client.close()
        except:
            pass
