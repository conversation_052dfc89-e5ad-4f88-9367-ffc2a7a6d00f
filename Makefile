# AutoGen + ClickHouse Demo Makefile

.PHONY: help install setup start stop test clean demo notebook ollama

# Default target
help:
	@echo "AutoGen + ClickHouse Demo"
	@echo "========================="
	@echo ""
	@echo "Available commands:"
	@echo "  make install      - Install Python dependencies"
	@echo "  make setup        - Quick setup (install + start + init)"
	@echo "  make setup-ollama - Setup with <PERSON><PERSON><PERSON> (install + start-ollama + init)"
	@echo "  make start        - Start ClickHouse with Docker"
	@echo "  make start-ollama - Start ClickHouse + Ollama with Docker"
	@echo "  make stop         - Stop services"
	@echo "  make init         - Initialize database with sample data"
	@echo "  make test         - Run tests"
	@echo "  make test-llm     - Test LLM configuration"
	@echo "  make demo         - Run the main demo"
	@echo "  make simple       - Run simple example"
	@echo "  make notebook     - Start Jupyter notebook"
	@echo "  make clean        - Clean up containers and data"
	@echo "  make logs         - Show service logs"
	@echo "  make ollama-setup - Setup Ollama (pull models, etc.)"
	@echo ""

# Install dependencies
install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements.txt

# Quick setup
setup: install start init
	@echo "✅ Setup complete! Run 'make demo' to start."

# Quick setup with Ollama
setup-ollama: install start-ollama init ollama-setup
	@echo "✅ Setup with Ollama complete! Run 'make demo' to start."

# Start ClickHouse
start:
	@echo "🐳 Starting ClickHouse..."
	docker-compose up -d
	@echo "⏳ Waiting for ClickHouse to be ready..."
	sleep 10

# Start ClickHouse + Ollama
start-ollama:
	@echo "🐳 Starting ClickHouse + Ollama..."
	docker-compose -f docker-compose.ollama.yml up -d
	@echo "⏳ Waiting for services to be ready..."
	sleep 15

# Stop services
stop:
	@echo "🛑 Stopping services..."
	docker-compose down || true
	docker-compose -f docker-compose.ollama.yml down || true

# Initialize database
init:
	@echo "🗄️ Initializing database..."
	python setup_database.py

# Run tests
test:
	@echo "🧪 Running tests..."
	python test_demo.py

# Run main demo
demo:
	@echo "🚀 Starting main demo..."
	python main.py

# Run simple example
simple:
	@echo "🚀 Starting simple example..."
	python simple_example.py

# Start Jupyter notebook
notebook:
	@echo "📓 Starting Jupyter notebook..."
	jupyter notebook demo_notebook.ipynb

# Clean up
clean:
	@echo "🧹 Cleaning up..."
	docker-compose down -v
	docker system prune -f

# Show logs
logs:
	@echo "📋 Service logs:"
	docker-compose logs -f clickhouse || docker-compose -f docker-compose.ollama.yml logs -f

# Ollama-specific commands
ollama-setup:
	@echo "🦙 Setting up Ollama..."
	python ollama_setup.py

ollama-pull:
	@echo "📥 Pulling Ollama model..."
	@read -p "Enter model name (e.g., llama2:7b): " model; \
	docker exec ollama-demo ollama pull $$model

ollama-list:
	@echo "📦 Available Ollama models:"
	docker exec ollama-demo ollama list || ollama list

ollama-logs:
	@echo "📋 Ollama logs:"
	docker-compose -f docker-compose.ollama.yml logs -f ollama

# Check status
status:
	@echo "📊 Service status:"
	docker-compose ps

# Environment setup
env:
	@if [ ! -f .env ]; then \
		echo "📝 Creating .env file..."; \
		cp .env.example .env; \
		echo "⚠️  Please edit .env file with your configuration"; \
	else \
		echo "✅ .env file already exists"; \
	fi

# Development setup
dev: env install start init test
	@echo "🔧 Development environment ready!"

# Production setup (without Docker)
prod: env install init
	@echo "🚀 Production setup complete!"
	@echo "⚠️  Make sure ClickHouse is running externally"
