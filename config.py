import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# LLM Provider Configuration
LLM_PROVIDER = os.getenv("LLM_PROVIDER", "ollama").lower()

def get_llm_config():
    """Get LLM configuration based on provider."""
    temperature = float(os.getenv("TEMPERATURE", "0.7"))

    if LLM_PROVIDER == "openai":
        config = {
            "model": os.getenv("OPENAI_MODEL", "gpt-4"),
            "api_key": os.getenv("OPENAI_API_KEY"),
            "temperature": temperature,
        }
        # Add base_url if specified (for OpenAI-compatible APIs)
        base_url = os.getenv("OPENAI_BASE_URL")
        if base_url and base_url != "https://api.openai.com/v1":
            config["base_url"] = base_url
        return config
    elif LLM_PROVIDER == "ollama":
        return {
            "model": os.getenv("OLLAMA_MODEL", "llama2"),
            "base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
            "api_key": "ollama",  # Ollama doesn't require API key
            "temperature": temperature,
        }
    elif LLM_PROVIDER == "azure":
        return {
            "model": os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
            "api_key": os.getenv("AZURE_OPENAI_API_KEY"),
            "base_url": os.getenv("AZURE_OPENAI_ENDPOINT"),
            "api_version": os.getenv("AZURE_OPENAI_API_VERSION", "2023-12-01-preview"),
            "api_type": "azure",
            "temperature": temperature,
        }
    else:
        raise ValueError(f"Unsupported LLM provider: {LLM_PROVIDER}")

# Get the appropriate LLM configuration
LLM_CONFIG = get_llm_config()

# Backward compatibility
OPENAI_CONFIG = LLM_CONFIG

# ClickHouse Configuration
CLICKHOUSE_CONFIG = {
    "host": os.getenv("CLICKHOUSE_HOST", "localhost"),
    "port": int(os.getenv("CLICKHOUSE_PORT", "8123")),
    "user": os.getenv("CLICKHOUSE_USER", "default"),
    "password": os.getenv("CLICKHOUSE_PASSWORD", ""),
    "database": os.getenv("CLICKHOUSE_DATABASE", "demo_db"),
    "secure": os.getenv("CLICKHOUSE_SECURE", "false").lower() == "true",
}

# Agent Configuration
AGENT_CONFIG = {
    "max_consecutive_auto_reply": int(os.getenv("MAX_CONSECUTIVE_AUTO_REPLY", "10")),
    "human_input_mode": "NEVER",  # Change to "ALWAYS" for interactive mode
}

# Database Schema
SAMPLE_TABLES = {
    "sales": {
        "columns": [
            "id UInt32",
            "product_id UInt32", 
            "customer_id UInt32",
            "sale_date Date",
            "quantity UInt32",
            "unit_price Float64",
            "total_amount Float64",
            "region String"
        ],
        "engine": "MergeTree()",
        "order_by": "sale_date"
    },
    "products": {
        "columns": [
            "id UInt32",
            "name String",
            "category String",
            "price Float64",
            "created_date Date"
        ],
        "engine": "MergeTree()",
        "order_by": "id"
    },
    "customers": {
        "columns": [
            "id UInt32",
            "name String",
            "email String",
            "registration_date Date",
            "country String",
            "age UInt8"
        ],
        "engine": "MergeTree()",
        "order_by": "id"
    }
}
