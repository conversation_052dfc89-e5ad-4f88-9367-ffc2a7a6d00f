<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Display Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-white mb-8">Data Display Test</h1>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 mb-6">
            <h2 class="text-xl font-semibold text-white mb-4">Test Data Table Generation</h2>
            
            <button id="test-table-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg mb-4">
                Generate Test Table
            </button>
            
            <div id="table-container" class="mt-4">
                <!-- Table will be generated here -->
            </div>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 mb-6">
            <h2 class="text-xl font-semibold text-white mb-4">Test Result Display</h2>
            
            <button id="test-result-btn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg mb-4">
                Generate Test Result
            </button>
            
            <div id="result-container" class="mt-4">
                <!-- Result will be generated here -->
            </div>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <h2 class="text-xl font-semibold text-white mb-4">Debug Console</h2>
            <div id="debug-console" class="bg-black/50 text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                <!-- Debug messages will appear here -->
            </div>
        </div>
    </div>

    <script src="/static/js/utils.js"></script>
    <script>
        // Override console.log to show in debug console
        const originalLog = console.log;
        const debugConsole = document.getElementById('debug-console');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            debugConsole.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
        };
        
        // Test data
        const testData = [
            { name: "customers", rows: 500 },
            { name: "sales", rows: 1000 },
            { name: "products", rows: 200 },
            { name: "traffic_logs_with_geo", rows: 5000 }
        ];
        
        const testResult = {
            question: "What tables do we have?",
            answer: "Here are the tables in your database",
            sql: "SHOW TABLES",
            data: testData,
            execution_time: 2.5,
            timestamp: new Date().toISOString()
        };
        
        // Test table generation
        document.getElementById('test-table-btn').addEventListener('click', () => {
            console.log('🧪 Testing table generation...');
            console.log('Test data:', testData);
            
            const tableHtml = createDataTable(testData);
            console.log('Generated table HTML:', tableHtml);
            
            document.getElementById('table-container').innerHTML = tableHtml;
        });
        
        // Test result display
        document.getElementById('test-result-btn').addEventListener('click', () => {
            console.log('🧪 Testing result display...');
            console.log('Test result:', testResult);
            
            // Simulate the displayResults function
            const results = [testResult];
            displayResults(results);
            
            // Also show in result container
            document.getElementById('result-container').innerHTML = `
                <div class="bg-white rounded-xl p-6">
                    <h3 class="text-lg font-semibold mb-4">Generated Result</h3>
                    <div id="results-container"></div>
                </div>
            `;
        });
        
        console.log('🚀 Test page loaded');
    </script>
</body>
</html>
