#!/usr/bin/env python3
"""
Test script for LLM configuration validation.
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openai_config():
    """Test OpenAI configuration."""
    print("🔍 Testing OpenAI Configuration")
    print("-" * 40)
    
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    model = os.getenv("OPENAI_MODEL", "gpt-4")
    
    if not api_key:
        print("❌ OPENAI_API_KEY not set")
        return False
    
    print(f"API Key: {api_key[:10]}..." if len(api_key) > 10 else f"API Key: {api_key}")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    
    # Test API connection
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Test with a simple completion
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "Hello! Please respond with 'API test successful.'"}
            ],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        response = requests.post(
            f"{base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result['choices'][0]['message']['content']
            print(f"✅ API test successful!")
            print(f"Response: {message}")
            return True
        else:
            print(f"❌ API test failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_ollama_config():
    """Test Ollama configuration."""
    print("🔍 Testing Ollama Configuration")
    print("-" * 40)
    
    base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    model = os.getenv("OLLAMA_MODEL", "llama2")
    
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    
    # Test server connection
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=5)
        if response.status_code == 200:
            models_data = response.json()
            available_models = [m['name'] for m in models_data.get('models', [])]
            print(f"✅ Ollama server is running")
            print(f"Available models: {available_models}")
            
            # Check if specified model is available
            if any(model in m for m in available_models):
                print(f"✅ Model '{model}' is available")
                
                # Test model
                test_data = {
                    "model": model,
                    "prompt": "Hello! Please respond with 'Model test successful.'",
                    "stream": False
                }
                
                response = requests.post(
                    f"{base_url}/api/generate",
                    json=test_data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    message = result.get('response', '')
                    print(f"✅ Model test successful!")
                    print(f"Response: {message}")
                    return True
                else:
                    print(f"❌ Model test failed: {response.text}")
                    return False
            else:
                print(f"❌ Model '{model}' not found")
                print(f"Available models: {available_models}")
                print(f"Pull the model with: ollama pull {model}")
                return False
        else:
            print(f"❌ Ollama server error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Ollama server: {e}")
        print("Make sure Ollama is running:")
        print("  - Local: ollama serve")
        print("  - Docker: docker-compose -f docker-compose.ollama.yml up -d")
        return False

def test_azure_config():
    """Test Azure OpenAI configuration."""
    print("🔍 Testing Azure OpenAI Configuration")
    print("-" * 40)
    
    api_key = os.getenv("AZURE_OPENAI_API_KEY")
    endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
    api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2023-12-01-preview")
    
    if not all([api_key, endpoint, deployment]):
        missing = []
        if not api_key: missing.append("AZURE_OPENAI_API_KEY")
        if not endpoint: missing.append("AZURE_OPENAI_ENDPOINT")
        if not deployment: missing.append("AZURE_OPENAI_DEPLOYMENT_NAME")
        print(f"❌ Missing required variables: {missing}")
        return False
    
    print(f"API Key: {api_key[:10]}..." if len(api_key) > 10 else f"API Key: {api_key}")
    print(f"Endpoint: {endpoint}")
    print(f"Deployment: {deployment}")
    print(f"API Version: {api_version}")
    
    # Test API connection
    try:
        headers = {
            "api-key": api_key,
            "Content-Type": "application/json"
        }
        
        url = f"{endpoint}/openai/deployments/{deployment}/chat/completions?api-version={api_version}"
        
        data = {
            "messages": [
                {"role": "user", "content": "Hello! Please respond with 'Azure API test successful.'"}
            ],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            message = result['choices'][0]['message']['content']
            print(f"✅ Azure API test successful!")
            print(f"Response: {message}")
            return True
        else:
            print(f"❌ Azure API test failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_autogen_integration():
    """Test AutoGen integration with current LLM configuration."""
    print("🔍 Testing AutoGen Integration")
    print("-" * 40)
    
    try:
        from config import LLM_PROVIDER, LLM_CONFIG
        from agents import get_llm_config_for_autogen
        import autogen
        
        print(f"LLM Provider: {LLM_PROVIDER}")
        print(f"LLM Config: {LLM_CONFIG}")
        
        # Create a simple test agent
        llm_config = get_llm_config_for_autogen()
        
        test_agent = autogen.AssistantAgent(
            name="TestAgent",
            system_message="You are a helpful assistant. Respond briefly.",
            llm_config=llm_config,
            max_consecutive_auto_reply=1,
        )
        
        user_proxy = autogen.UserProxyAgent(
            name="User",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1,
            is_termination_msg=lambda x: True,
            code_execution_config=False,
        )
        
        print("✅ AutoGen agents created successfully")
        
        # Test a simple conversation
        print("Testing agent conversation...")
        user_proxy.initiate_chat(
            test_agent,
            message="Hello! Please respond with 'AutoGen integration test successful.'"
        )
        
        print("✅ AutoGen integration test completed")
        return True
        
    except Exception as e:
        print(f"❌ AutoGen integration error: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 LLM Configuration Test Suite")
    print("=" * 50)
    
    # Get LLM provider
    llm_provider = os.getenv("LLM_PROVIDER", "ollama").lower()
    print(f"Current LLM Provider: {llm_provider}")
    print()
    
    success = False
    
    # Test based on provider
    if llm_provider == "openai":
        success = test_openai_config()
    elif llm_provider == "ollama":
        success = test_ollama_config()
    elif llm_provider == "azure":
        success = test_azure_config()
    else:
        print(f"❌ Unknown LLM provider: {llm_provider}")
        return 1
    
    print()
    
    # Test AutoGen integration if basic test passed
    if success:
        test_autogen_integration()
    
    print()
    print("=" * 50)
    if success:
        print("🎉 Configuration test completed successfully!")
        print("You can now run the demo: python main.py")
    else:
        print("❌ Configuration test failed!")
        print("Please check your .env file and try again.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
