// NEW Utils file to bypass cache - Version 2025-08-01-14:25
console.log('🚀 NEW Utils.js loaded successfully!');

// Simple table generator that WILL work
function createDataTableNew(data) {
    console.log('🔥 NEW createDataTableNew called with:', data);
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('❌ No valid data');
        return '<div class="bg-red-100 p-4 rounded"><p class="text-red-600">No data to display</p></div>';
    }
    
    const firstRow = data[0];
    if (!firstRow || typeof firstRow !== 'object') {
        console.log('❌ Invalid first row');
        return '<div class="bg-red-100 p-4 rounded"><p class="text-red-600">Invalid data format</p></div>';
    }
    
    const columns = Object.keys(firstRow);
    console.log('🔥 Columns found:', columns);
    
    let html = `
        <div class="bg-white border rounded-lg overflow-hidden shadow">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        ${columns.map(col => `<th class="px-4 py-2 text-left font-medium text-gray-700 border-b">${col}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${data.slice(0, 10).map((row, index) => `
                        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}">
                            ${columns.map(col => `<td class="px-4 py-2 border-b text-gray-900">${row[col] || ''}</td>`).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            ${data.length > 10 ? `<div class="px-4 py-2 bg-gray-50 text-sm text-gray-600">Showing 10 of ${data.length} rows</div>` : ''}
        </div>
    `;
    
    console.log('🔥 NEW table HTML generated successfully!');
    return html;
}

// Override the displayResults function
function displayResultsNew(results) {
    console.log('🔥 NEW displayResults called with:', results);
    
    if (!results || results.length === 0) {
        document.getElementById('no-results').style.display = 'block';
        document.getElementById('results-container').style.display = 'none';
        return;
    }

    document.getElementById('no-results').style.display = 'none';
    document.getElementById('results-container').style.display = 'block';

    const resultsContainer = document.getElementById('results-list');
    resultsContainer.innerHTML = '';

    results.forEach((result, index) => {
        console.log(`🔥 Processing result ${index}:`, result);
        
        const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';
        const formattedAnswer = result.answer || 'No answer received';

        let dataSection = '';
        if (result.data && result.data.length > 0) {
            console.log('🔥 Creating table for result data:', result.data);
            const tableHtml = createDataTableNew(result.data);
            
            dataSection = `
                <div class="mt-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <i class="material-icons text-blue-600 mr-2">table_view</i>
                            <h4 class="text-lg font-medium text-gray-800">Query Results</h4>
                            <span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">${result.data.length} rows</span>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="downloadData(${index}, 'csv')" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                                CSV
                            </button>
                            <button onclick="downloadData(${index}, 'json')" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                                JSON
                            </button>
                        </div>
                    </div>
                    ${tableHtml}
                </div>
            `;
        }

        const resultCard = document.createElement('div');
        resultCard.className = 'bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200';
        resultCard.innerHTML = `
            <div class="flex justify-between items-start mb-4">
                <div class="flex items-center">
                    <i class="material-icons text-blue-600 mr-2">help_outline</i>
                    <h3 class="text-lg font-medium text-gray-800">Question</h3>
                </div>
                <span class="text-gray-500 text-sm">${timestamp}</span>
            </div>
            
            <div class="bg-blue-50 p-4 rounded-lg mb-6">
                <p class="text-gray-700">${result.question}</p>
            </div>

            <div class="flex items-center mb-4">
                <i class="material-icons text-green-600 mr-2">psychology</i>
                <h3 class="text-lg font-medium text-gray-800">AI Answer</h3>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <p class="text-gray-700">${formattedAnswer}</p>
            </div>

            ${dataSection}

            <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                <div class="flex items-center text-gray-500">
                    <i class="material-icons text-sm mr-1">timer</i>
                    <span class="text-sm">Execution time: ${(result.execution_time || 0).toFixed(2)}s</span>
                </div>
                <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                    <i class="material-icons text-sm mr-1">check_circle</i>
                    Complete
                </div>
            </div>
        `;

        resultsContainer.appendChild(resultCard);
    });
    
    console.log('🔥 NEW displayResults completed successfully!');
}

// Simple download function
function downloadData(index, format) {
    console.log('🔥 Download requested:', index, format);
    alert(`Download ${format} for result ${index} - Feature coming soon!`);
}

console.log('🔥 NEW Utils.js fully loaded and ready!');
