// AI Data Analysis Frontend Application
class DataAnalysisApp {
    constructor() {
        this.loading = false;
        this.results = [];
        this.dbInfo = null;
        this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        this.exampleQuestions = [
            "What tables do we have?",
            "Show me sales by region AND product categories",
            "What are our top selling regions?",
            "Show me the products sold in those regions",
            "Analyze customer demographics and sales performance",
            "Create a comprehensive business intelligence report",
            "Compare performance across all dimensions"
        ];

        this.sqlExamples = [
            "SELECT * FROM sales LIMIT 10",
            "SELECT region, SUM(total_amount) FROM sales GROUP BY region",
            "SELECT p.name, p.category, SUM(s.total_amount) FROM sales s JOIN products p ON s.product_id = p.id GROUP BY p.name, p.category",
            "SELECT COUNT(*) FROM customers WHERE country = 'China'",
            "SELECT DATE_FORMAT(sale_date, '%Y-%m') as month, SUM(total_amount) FROM sales GROUP BY month"
        ];
    }

    async init() {
        console.log('Initializing AI Data Analysis App...');
        this.renderUI();
        this.setupEventHandlers();
        this.setupTaskManager();
        await this.loadDatabaseInfo();
        this.populateExamples();

        // Initial Analytics update
        setTimeout(() => {
            this.updateAnalyticsTab();
        }, 1000);
    }

    setupTaskManager() {
        // Setup task manager event listeners
        taskManager.on('taskCompleted', (task) => {
            console.log('🎉 Task completed:', task);
            console.log('Task type:', task.type);
            console.log('Task result:', task.result);

            // Update Analytics tab
            this.updateAnalyticsTab();

            // Handle different task types
            switch (task.type) {
                case 'ai_query':
                    console.log('📊 Handling AI query result');
                    this.handleAIQueryResult(task.result);
                    break;
                case 'sql_execute':
                    console.log('💾 Handling SQL result');
                    this.handleSQLResult(task.result);
                    break;
                case 'database-info':
                    console.log('🗄️ Database info task completed');
                    break;
                default:
                    console.log('❓ Unknown task type:', task.type);
            }
        });

        taskManager.on('taskFailed', (task) => {
            console.error('Task failed:', task);
            showError(`Task failed: ${task.error.message}`);

            // Update Analytics tab
            this.updateAnalyticsTab();
        });

        taskManager.on('taskProgress', (task) => {
            console.log(`Task progress: ${task.progress}% - ${task.progressMessage}`);
        });
    }

    renderUI() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="app-container">
                <div class="container mx-auto px-6 py-8">
                    <!-- Header -->
                    <div class="card scale-in">
                        <div class="card-header">
                            <i data-lucide="brain-circuit"></i>
                            AI Data Analysis Platform
                        </div>
                        <p class="text-white/80 text-lg font-medium">Intelligent data analysis with natural language queries and advanced SQL execution</p>
                    </div>
                
                <!-- Database Info -->
                <div class="card fade-in">
                    <div class="card-header">
                        <i data-lucide="database"></i>
                        Database Overview
                    </div>
                    <div id="db-loading" class="flex items-center text-white/70">
                        <div class="loading-spinner w-5 h-5 border-2 border-white/30 border-t-white rounded-full mr-3"></div>
                        Loading database information...
                    </div>
                    <div id="db-info" class="grid grid-cols-1 md:grid-cols-3 gap-6" style="display: none;">
                        <!-- Database tables will be populated here -->
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="tab-container">
                    <ul class="tab-list">
                        <li>
                            <button class="tab-button active" data-tab="ai-chat">
                                <i data-lucide="message-circle" class="w-4 h-4 mr-2"></i>
                                AI Chat
                            </button>
                        </li>
                        <li>
                            <button class="tab-button" data-tab="sql-editor">
                                <i data-lucide="code" class="w-4 h-4 mr-2"></i>
                                SQL Editor
                            </button>
                        </li>
                        <li>
                            <button class="tab-button" data-tab="analytics">
                                <i data-lucide="bar-chart-3" class="w-4 h-4 mr-2"></i>
                                Analytics
                            </button>
                        </li>
                    </ul>
                </div>
                
                <!-- Tab Content: AI Chat -->
                <div id="ai-chat" class="tab-content active">
                    <div class="card slide-up">
                        <div class="card-header">
                            <i data-lucide="message-circle"></i>
                            AI Assistant
                        </div>

                        <!-- Example Questions -->
                        <div class="mb-6">
                            <p class="text-white/80 mb-3 flex items-center font-medium">
                                <i data-lucide="help-circle" class="w-4 h-4 mr-2"></i>
                                Try these examples:
                            </p>
                            <div id="example-questions" class="flex flex-wrap gap-3">
                                <!-- Example questions will be populated here -->
                            </div>
                        </div>

                        <!-- Query Input -->
                        <div class="flex gap-4 mb-6 flex-responsive">
                            <div class="flex-1 relative">
                                <input
                                    type="text"
                                    id="question-input"
                                    placeholder="Ask a question about your data..."
                                    class="input-field pr-12"
                                >
                                <i data-lucide="search" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/40 w-5 h-5"></i>
                            </div>
                            <button id="ask-button" class="btn btn-primary flex items-center">
                                <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                                <span id="button-text">Ask AI</span>
                            </button>
                        </div>

                        <!-- Advanced Options -->
                        <div class="bg-black/20 rounded-2xl p-6 space-y-4 backdrop-filter backdrop-blur-sm border border-white/10">
                            <h3 class="text-white font-semibold mb-4 flex items-center">
                                <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                                Advanced Options
                            </h3>
                            <label class="flex items-center text-white/80 cursor-pointer hover:text-white transition-colors">
                                <input type="checkbox" id="include-raw-data" class="mr-3 w-4 h-4 text-primary-600 rounded">
                                <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                                Include raw data in response
                            </label>
                            <label class="flex items-center text-white/80 cursor-pointer hover:text-white transition-colors">
                                <input type="checkbox" id="optimize-prompt" class="mr-3 w-4 h-4 text-primary-600 rounded">
                                <i data-lucide="zap" class="w-4 h-4 mr-2"></i>
                                Optimize prompt for better results
                            </label>
                            <label class="flex items-center text-white/80 cursor-pointer hover:text-white transition-colors">
                                <input type="checkbox" id="enable-task-decomposition" class="mr-3 w-4 h-4 text-primary-600 rounded">
                                <i data-lucide="git-branch" class="w-4 h-4 mr-2"></i>
                                Enable complex task decomposition
                            </label>
                            <label class="flex items-center text-white/80 cursor-pointer hover:text-white transition-colors">
                                <input type="checkbox" id="generate-charts" class="mr-3 w-4 h-4 text-primary-600 rounded">
                                <i data-lucide="bar-chart" class="w-4 h-4 mr-2"></i>
                                Auto-generate charts from data
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Tab Content: SQL Editor -->
                <div id="sql-editor" class="tab-content">
                    <div class="card slide-up">
                        <div class="card-header">
                            <i data-lucide="code"></i>
                            SQL Editor
                        </div>

                        <!-- SQL Input -->
                        <div class="mb-6">
                            <label for="sql-input" class="block text-white/80 mb-3 flex items-center font-medium">
                                <i data-lucide="edit" class="w-4 h-4 mr-2"></i>
                                SQL Query:
                            </label>
                            <textarea
                                id="sql-input"
                                placeholder="SELECT * FROM sales LIMIT 10;"
                                class="textarea-field"
                                rows="8"
                            ></textarea>
                        </div>

                        <!-- SQL Execution Controls -->
                        <div class="flex gap-4 mb-6 flex-responsive">
                            <button id="execute-sql-button" class="btn btn-success flex items-center">
                                <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                                <span id="sql-button-text">Execute Query</span>
                            </button>
                            <select id="export-format" class="select-field">
                                <option value="">No Export</option>
                                <option value="csv">📄 Export as CSV</option>
                                <option value="json">📋 Export as JSON</option>
                            </select>
                        </div>

                        <!-- SQL Examples -->
                        <div class="mb-4">
                            <p class="text-white/80 mb-3 flex items-center font-medium">
                                <i data-lucide="lightbulb" class="w-4 h-4 mr-2"></i>
                                Quick examples:
                            </p>
                            <div id="sql-examples" class="flex flex-wrap gap-3">
                                <!-- SQL examples will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Content: Analytics -->
                <div id="analytics" class="tab-content">
                    <div class="space-y-6">
                        <!-- Task Overview -->
                        <div class="card slide-up">
                            <div class="card-header">
                                <i data-lucide="activity"></i>
                                Task Monitor
                                <span id="active-task-count" class="ml-2 px-2 py-1 bg-blue-500/20 rounded-full text-xs text-blue-300">0 active</span>
                            </div>

                            <!-- Active Tasks -->
                            <div id="active-tasks-container" class="space-y-4">
                                <!-- Active tasks will be displayed here -->
                            </div>

                            <!-- No Active Tasks -->
                            <div id="no-active-tasks" class="text-center py-8">
                                <i data-lucide="check-circle" class="w-12 h-12 text-green-400 mx-auto mb-3"></i>
                                <h3 class="text-lg font-semibold text-white mb-2">All Tasks Complete</h3>
                                <p class="text-white/60">No active tasks running. Submit a query to see real-time progress.</p>
                            </div>
                        </div>

                        <!-- Completed Tasks -->
                        <div class="card slide-up">
                            <div class="card-header">
                                <i data-lucide="clipboard-check"></i>
                                Recent Results
                                <span id="completed-task-count" class="ml-2 px-2 py-1 bg-green-500/20 rounded-full text-xs text-green-300">0 completed</span>
                            </div>

                            <!-- Results Container -->
                            <div id="analytics-results-container" class="space-y-4">
                                <!-- Completed tasks and results will be displayed here -->
                            </div>

                            <!-- No Results -->
                            <div id="no-results-analytics" class="text-center py-8">
                                <i data-lucide="search" class="w-12 h-12 text-white/40 mx-auto mb-3"></i>
                                <h3 class="text-lg font-semibold text-white mb-2">No Results Yet</h3>
                                <p class="text-white/60">Submit your first query to see results and analytics here.</p>
                            </div>
                        </div>

                        <!-- Task Statistics -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="card slide-up">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-white/60 text-sm">Total Tasks</p>
                                        <p id="total-tasks-stat" class="text-2xl font-bold text-white">0</p>
                                    </div>
                                    <i data-lucide="layers" class="w-8 h-8 text-blue-400"></i>
                                </div>
                            </div>

                            <div class="card slide-up">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-white/60 text-sm">Success Rate</p>
                                        <p id="success-rate-stat" class="text-2xl font-bold text-green-400">100%</p>
                                    </div>
                                    <i data-lucide="trending-up" class="w-8 h-8 text-green-400"></i>
                                </div>
                            </div>

                            <div class="card slide-up">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-white/60 text-sm">Avg. Time</p>
                                        <p id="avg-time-stat" class="text-2xl font-bold text-purple-400">0s</p>
                                    </div>
                                    <i data-lucide="clock" class="w-8 h-8 text-purple-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                
                <!-- SQL Results Section -->
                <div id="sql-results-section" class="card scale-in" style="display: none;">
                    <div class="card-header">
                        <i data-lucide="activity"></i>
                        SQL Execution Results
                    </div>
                    <div id="sql-results-container">
                        <!-- SQL results will be displayed here -->
                    </div>
                </div>

                <!-- Results -->
                <div id="results-container" class="space-y-6" style="display: none;">
                    <div class="flex items-center mb-6">
                        <i data-lucide="clipboard-list" class="w-6 h-6 text-white mr-3"></i>
                        <h2 class="text-2xl font-bold text-white">Query Results</h2>
                    </div>
                    <div id="results-list">
                        <!-- Results will be populated here -->
                    </div>
                </div>

                <!-- Loading State -->
                <div id="loading-state" class="card text-center" style="display: none;">
                    <div class="flex flex-col items-center justify-center py-12">
                        <div class="relative mb-8">
                            <div class="loading-spinner w-16 h-16 border-4 border-white/20 border-t-white rounded-full"></div>
                            <i data-lucide="brain-circuit" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white w-8 h-8"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-3">AI is analyzing your question</h3>
                        <p class="text-white/70 text-lg">This may take a few seconds</p>
                        <div class="w-64 h-1 bg-white/20 rounded-full mt-6 overflow-hidden">
                            <div class="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>
                </div>

                <!-- Chart Display Section -->
                <div id="chart-section" class="card scale-in" style="display: none;">
                    <div class="card-header">
                        <i data-lucide="bar-chart-3"></i>
                        Data Visualization
                    </div>
                    <div id="chart-container" class="text-center">
                        <!-- Charts will be displayed here -->
                    </div>
                </div>

                <!-- Error State -->
                <div id="error-state" class="card bg-red-500/20 border-red-500/30 mb-6 scale-in" style="display: none;">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i data-lucide="alert-circle" class="w-6 h-6 text-red-400"></i>
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-lg font-semibold text-red-300 mb-2">Error</h3>
                            <div id="error-message" class="text-red-200 mb-4"></div>
                            <button id="dismiss-error" class="btn btn-secondary text-red-300 hover:text-red-200">
                                <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                                Dismiss
                            </button>
                        </div>
                    </div>
                </div>

                <!-- No Results Message -->
                <div id="no-results" class="text-center py-20">
                    <div class="mb-8">
                        <i data-lucide="brain-circuit" class="w-24 h-24 mx-auto text-white/30"></i>
                    </div>
                    <h3 class="text-3xl font-bold text-white mb-4">Ready to analyze your data!</h3>
                    <p class="text-xl text-white/70 max-w-2xl mx-auto mb-8">Ask questions about your ClickHouse database using natural language or execute SQL queries directly with our intelligent assistant.</p>
                    <div class="flex justify-center space-x-6">
                        <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
                            <i data-lucide="zap" class="w-5 h-5 mr-3 text-yellow-400"></i>
                            <span class="text-white font-medium">AI-Powered</span>
                        </div>
                        <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
                            <i data-lucide="rocket" class="w-5 h-5 mr-3 text-green-400"></i>
                            <span class="text-white font-medium">Fast Queries</span>
                        </div>
                        <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
                            <i data-lucide="download" class="w-5 h-5 mr-3 text-blue-400"></i>
                            <span class="text-white font-medium">Export Ready</span>
                        </div>
                    </div>
                </div>

                <!-- Task Management Menu -->
                <div class="fixed top-6 right-6 z-40">
                    <div class="flex flex-col space-y-3">
                        <!-- Task Manager Toggle -->
                        <button id="task-menu-toggle" class="w-12 h-12 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center transition-all duration-300 group">
                            <i data-lucide="menu" class="w-5 h-5 text-white group-hover:scale-110 transition-transform"></i>
                        </button>

                        <!-- Quick Actions Menu -->
                        <div id="quick-actions-menu" class="hidden flex-col space-y-2">
                            <button id="demo-task-btn" class="w-12 h-12 bg-blue-500/20 hover:bg-blue-500/30 backdrop-blur-sm rounded-xl border border-blue-500/30 flex items-center justify-center transition-all duration-300 group" title="Run Demo Task">
                                <i data-lucide="play" class="w-5 h-5 text-blue-400 group-hover:scale-110 transition-transform"></i>
                            </button>
                            <button id="batch-query-btn" class="w-12 h-12 bg-green-500/20 hover:bg-green-500/30 backdrop-blur-sm rounded-xl border border-green-500/30 flex items-center justify-center transition-all duration-300 group" title="Batch Queries">
                                <i data-lucide="layers" class="w-5 h-5 text-green-400 group-hover:scale-110 transition-transform"></i>
                            </button>
                            <button id="export-all-btn" class="w-12 h-12 bg-purple-500/20 hover:bg-purple-500/30 backdrop-blur-sm rounded-xl border border-purple-500/30 flex items-center justify-center transition-all duration-300 group" title="Export All Data">
                                <i data-lucide="download" class="w-5 h-5 text-purple-400 group-hover:scale-110 transition-transform"></i>
                            </button>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    setupEventHandlers() {
        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const tabId = e.target.closest('.tab-button').dataset.tab;
                this.switchTab(tabId);
            });
        });

        // Ask button click
        document.getElementById('ask-button').addEventListener('click', () => {
            this.submitQuery();
        });

        // Execute SQL button click
        document.getElementById('execute-sql-button').addEventListener('click', () => {
            this.executeSQLQuery();
        });

        // Enter key in input
        document.getElementById('question-input').addEventListener('keypress', (e) => {
            if (e.which === 13) { // Enter key
                this.submitQuery();
            }
        });

        // Ctrl+Enter in SQL textarea
        document.getElementById('sql-input').addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.which === 13) { // Ctrl+Enter
                this.executeSQLQuery();
            }
        });

        // Example question clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('example-question')) {
                const question = e.target.dataset.question;
                document.getElementById('question-input').value = question;
            }
        });

        // SQL example clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('sql-example')) {
                const sql = e.target.dataset.sql;
                document.getElementById('sql-input').value = sql;
            }
        });

        // Dismiss error
        document.getElementById('dismiss-error').addEventListener('click', () => {
            hideError();
        });

        // Task menu toggle
        document.getElementById('task-menu-toggle').addEventListener('click', () => {
            this.toggleQuickActionsMenu();
        });

        // Quick action buttons
        document.getElementById('demo-task-btn').addEventListener('click', () => {
            this.runDemoTask();
        });

        document.getElementById('batch-query-btn').addEventListener('click', () => {
            this.runBatchQueries();
        });

        document.getElementById('export-all-btn').addEventListener('click', () => {
            this.exportAllData();
        });

        // Add test result button for debugging
        const testButton = document.createElement('button');
        testButton.textContent = 'Test Result Display';
        testButton.className = 'btn-primary';
        testButton.style.position = 'fixed';
        testButton.style.top = '10px';
        testButton.style.right = '10px';
        testButton.style.zIndex = '9999';
        testButton.addEventListener('click', () => {
            this.testResultDisplay();
        });
        document.body.appendChild(testButton);
    }

    toggleQuickActionsMenu() {
        const menu = document.getElementById('quick-actions-menu');
        if (menu.classList.contains('hidden')) {
            menu.classList.remove('hidden');
            menu.classList.add('flex');
        } else {
            menu.classList.add('hidden');
            menu.classList.remove('flex');
        }
    }

    async runDemoTask() {
        // Create a demo SQL query task for testing
        const demoSQL = 'SELECT COUNT(*) as demo_count FROM information_schema.tables';

        try {
            const taskId = await taskManager.createSQLExecuteTask(demoSQL, null);
            console.log('Demo task created:', taskId);
        } catch (error) {
            console.error('Error creating demo task:', error);
            showError(`Failed to create demo task: ${error.message}`);
        }
    }

    async runBatchQueries() {
        // For now, just run a sample query since we don't have batch support yet
        const sampleQuery = 'SELECT table_name, table_rows FROM information_schema.tables WHERE table_schema = database() LIMIT 5';

        try {
            const taskId = await taskManager.createSQLExecuteTask(sampleQuery, null);
            console.log('Batch query task created:', taskId);
        } catch (error) {
            console.error('Error creating batch query task:', error);
            showError(`Failed to create batch query task: ${error.message}`);
        }
    }

    async exportAllData() {
        if (this.results.length === 0) {
            showError('No data available to export');
            return;
        }

        try {
            // Export data directly for now
            const dataToExport = JSON.stringify(this.results, null, 2);
            const blob = new Blob([dataToExport], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `query_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('Data exported successfully');
        } catch (error) {
            console.error('Error exporting data:', error);
            showError(`Failed to export data: ${error.message}`);
        }
    }

    switchTab(tabId) {
        // Remove active class from all tabs and content
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

        // Add active class to selected tab and content
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
        document.getElementById(tabId).classList.add('active');

        // Re-initialize Lucide icons for the new content
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    async loadDatabaseInfo() {
        try {
            console.log('Loading database info...');
            const response = await fetch('/api/database-info');
            this.dbInfo = await response.json();
            console.log('Database info loaded:', this.dbInfo);
            this.displayDatabaseInfo();
        } catch (error) {
            console.error('Error loading database info:', error);
        }
    }

    displayDatabaseInfo() {
        if (!this.dbInfo) return;

        document.getElementById('db-loading').style.display = 'none';

        const dbInfoContainer = document.getElementById('db-info');
        dbInfoContainer.innerHTML = '';

        if (this.dbInfo.tables && this.dbInfo.tables.length > 0) {
            this.dbInfo.tables.forEach((table, index) => {
                const recordCount = this.dbInfo.total_records ? this.dbInfo.total_records[table] || 0 : 0;
                const tableCard = document.createElement('div');
                tableCard.className = 'bg-white/10 backdrop-blur-sm p-6 rounded-2xl border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-300 cursor-pointer fade-in group';
                tableCard.style.animationDelay = `${index * 0.1}s`;
                tableCard.innerHTML = `
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                            <i data-lucide="table" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">${table}</h3>
                            <p class="text-white/60 text-sm">${recordCount.toLocaleString()} records</p>
                        </div>
                    </div>
                    <div class="flex items-center text-white/50 text-sm">
                        <i data-lucide="database" class="w-4 h-4 mr-2"></i>
                        <span>Click to explore</span>
                    </div>
                `;

                // Add click handler to show table details
                tableCard.addEventListener('click', () => {
                    this.showTableDetails(table);
                });

                dbInfoContainer.appendChild(tableCard);

                // Initialize Lucide icons for this card
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            });
        }

        dbInfoContainer.style.display = 'grid';
    }

    populateExamples() {
        // Populate example questions
        const questionsContainer = document.getElementById('example-questions');
        questionsContainer.innerHTML = '';

        this.exampleQuestions.forEach((question, index) => {
            const button = document.createElement('button');
            button.className = 'example-question btn btn-secondary fade-in flex items-center';
            button.style.animationDelay = `${index * 0.05}s`;
            button.dataset.question = question;
            button.innerHTML = `
                <i data-lucide="help-circle" class="w-4 h-4 mr-2"></i>
                ${question}
            `;
            questionsContainer.appendChild(button);
        });

        // Populate SQL examples
        const sqlContainer = document.getElementById('sql-examples');
        sqlContainer.innerHTML = '';

        this.sqlExamples.forEach((sql, index) => {
            const button = document.createElement('button');
            button.className = 'sql-example btn btn-sql-example fade-in flex items-center';
            button.style.animationDelay = `${index * 0.05}s`;
            button.dataset.sql = sql;
            button.innerHTML = `
                <i data-lucide="code" class="w-4 h-4 mr-2"></i>
                <span class="font-mono text-xs">${sql}</span>
            `;
            sqlContainer.appendChild(button);
        });

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    showTableDetails(tableName) {
        // Create a modern modal for table information
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 fade-in';
        modal.innerHTML = `
            <div class="bg-white/10 backdrop-blur-xl rounded-3xl p-8 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto border border-white/20 shadow-2xl">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                            <i data-lucide="table" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-white">${tableName}</h3>
                            <p class="text-white/60">${(this.dbInfo.total_records[tableName] || 0).toLocaleString()} records</p>
                        </div>
                    </div>
                    <button class="close-modal w-10 h-10 bg-white/10 hover:bg-white/20 rounded-xl flex items-center justify-center transition-colors">
                        <i data-lucide="x" class="w-5 h-5 text-white"></i>
                    </button>
                </div>
                <div class="space-y-6">
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="zap" class="w-5 h-5 mr-2"></i>
                            Quick Actions
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <button class="quick-action bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl p-4 text-left transition-all group" data-sql="SELECT * FROM ${tableName} LIMIT 10">
                                <div class="flex items-center mb-2">
                                    <i data-lucide="eye" class="w-5 h-5 text-blue-400 mr-2"></i>
                                    <span class="text-white font-medium">View Sample</span>
                                </div>
                                <p class="text-white/60 text-sm">Show first 10 rows</p>
                            </button>
                            <button class="quick-action bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl p-4 text-left transition-all group" data-sql="SELECT COUNT(*) FROM ${tableName}">
                                <div class="flex items-center mb-2">
                                    <i data-lucide="hash" class="w-5 h-5 text-green-400 mr-2"></i>
                                    <span class="text-white font-medium">Count Records</span>
                                </div>
                                <p class="text-white/60 text-sm">Total row count</p>
                            </button>
                            <button class="quick-action bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl p-4 text-left transition-all group" data-sql="DESCRIBE ${tableName}">
                                <div class="flex items-center mb-2">
                                    <i data-lucide="info" class="w-5 h-5 text-purple-400 mr-2"></i>
                                    <span class="text-white font-medium">Describe Table</span>
                                </div>
                                <p class="text-white/60 text-sm">Table structure</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-modal').addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Quick action buttons
        modal.querySelectorAll('.quick-action').forEach(button => {
            button.addEventListener('click', () => {
                const sql = button.dataset.sql;
                document.getElementById('sql-input').value = sql;
                this.switchTab('sql-editor');
                modal.remove();
            });
        });

        document.body.appendChild(modal);

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    async submitQuery() {
        console.log('submitQuery called');

        const question = document.getElementById('question-input').value.trim();
        if (!question) {
            showError('Please enter a question');
            return;
        }

        // Get options
        const includeRawData = document.getElementById('include-raw-data').checked;
        const optimizePrompt = document.getElementById('optimize-prompt').checked;
        const enableTaskDecomposition = document.getElementById('enable-task-decomposition').checked;
        const generateCharts = document.getElementById('generate-charts').checked;

        // Clear the input
        document.getElementById('question-input').value = '';

        try {
            // Create async task on server
            const taskId = await taskManager.createAIQueryTask(question, {
                includeRawData,
                optimizePrompt,
                enableTaskDecomposition,
                generateCharts,
                sessionId: this.sessionId
            });

            console.log('AI query task created:', taskId);
            return taskId;

        } catch (error) {
            console.error('Error creating AI query task:', error);
            showError(`Failed to create query task: ${error.message}`);
        }
    }

    handleAIQueryResult(result) {
        console.log('🔄 Processing AI query result:', result);

        if (!result) {
            console.error('❌ No result data received');
            showError('No result data received from AI query');
            return;
        }

        // Add to results
        this.results.unshift(result);
        console.log('📝 Added result to array, total results:', this.results.length);

        // Display results
        console.log('🖥️ Calling displayResults with:', this.results);

        // Try new display function first
        if (typeof displayResultsNew === 'function') {
            console.log('🔥 Using NEW displayResults function');
            displayResultsNew(this.results);
        } else {
            console.log('📊 Using original displayResults function');
            displayResults(this.results);
        }

        // Display chart if available
        if (result.raw_data && result.raw_data.chart_data) {
            console.log('📊 Displaying chart data');
            this.displayChart(result.raw_data.chart_data);
        }

        console.log('✅ AI query result processed successfully');
    }

    testResultDisplay() {
        console.log('🧪 Testing result display');

        // Create a test result
        const testResult = {
            question: 'Test question: What tables do we have?',
            answer: 'Here are the tables in your database',
            sql: 'SHOW TABLES',
            data: [
                { table_name: 'sales' },
                { table_name: 'customers' },
                { table_name: 'products' }
            ],
            execution_time: 2.5,
            timestamp: new Date().toISOString()
        };

        console.log('🔄 Adding test result:', testResult);
        this.results.unshift(testResult);

        console.log('🖥️ Calling displayResults with test data');
        displayResults(this.results);

        console.log('✅ Test result display completed');
    }

    updateAnalyticsTab() {
        console.log('📊 Updating Analytics tab');

        // Get all tasks from task manager
        const allTasks = Array.from(taskManager.tasks.values());

        // Categorize tasks
        const activeTasks = allTasks.filter(task =>
            task.status === taskManager.STATUS.PENDING ||
            task.status === taskManager.STATUS.PLANNING ||
            task.status === taskManager.STATUS.EXECUTING
        );

        const completedTasks = allTasks.filter(task =>
            task.status === taskManager.STATUS.COMPLETED
        );

        const failedTasks = allTasks.filter(task =>
            task.status === taskManager.STATUS.FAILED
        );

        // Update active tasks count
        const activeCountElement = document.getElementById('active-task-count');
        if (activeCountElement) {
            activeCountElement.textContent = `${activeTasks.length} active`;
            activeCountElement.className = activeTasks.length > 0
                ? 'ml-2 px-2 py-1 bg-blue-500/20 rounded-full text-xs text-blue-300 animate-pulse'
                : 'ml-2 px-2 py-1 bg-gray-500/20 rounded-full text-xs text-gray-400';
        }

        // Update completed tasks count
        const completedCountElement = document.getElementById('completed-task-count');
        if (completedCountElement) {
            completedCountElement.textContent = `${completedTasks.length} completed`;
        }

        // Update active tasks container
        this.updateActiveTasksContainer(activeTasks);

        // Update completed tasks container
        this.updateCompletedTasksContainer(completedTasks);

        // Update statistics
        this.updateTaskStatistics(allTasks, completedTasks, failedTasks);
    }

    updateActiveTasksContainer(activeTasks) {
        const container = document.getElementById('active-tasks-container');
        const noTasksElement = document.getElementById('no-active-tasks');

        if (!container || !noTasksElement) return;

        if (activeTasks.length === 0) {
            container.style.display = 'none';
            noTasksElement.style.display = 'block';
            return;
        }

        container.style.display = 'block';
        noTasksElement.style.display = 'none';

        container.innerHTML = activeTasks.map(task => this.createActiveTaskCard(task)).join('');

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    createActiveTaskCard(task) {
        const statusIcon = this.getTaskStatusIcon(task.status);
        const statusColor = this.getTaskStatusColor(task.status);
        const currentStep = task.steps && task.steps[task.currentStepIndex];

        return `
            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i data-lucide="${statusIcon}" class="w-5 h-5 ${statusColor} mr-2"></i>
                        <h4 class="text-white font-medium">${task.name}</h4>
                    </div>
                    <span class="text-white/60 text-sm">${Math.round(task.progress)}%</span>
                </div>

                ${currentStep ? `
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-white/80 text-sm">${currentStep.name}</span>
                            <span class="text-white/60 text-xs">${task.currentStepIndex + 1}/${task.steps.length}</span>
                        </div>
                        <p class="text-white/60 text-xs mb-2">${currentStep.description}</p>
                    </div>
                ` : ''}

                <div class="w-full bg-white/20 rounded-full h-2">
                    <div class="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full transition-all duration-500"
                         style="width: ${task.progress}%"></div>
                </div>
            </div>
        `;
    }

    updateCompletedTasksContainer(completedTasks) {
        const container = document.getElementById('analytics-results-container');
        const noResultsElement = document.getElementById('no-results-analytics');

        if (!container || !noResultsElement) return;

        if (completedTasks.length === 0) {
            container.style.display = 'none';
            noResultsElement.style.display = 'block';
            return;
        }

        container.style.display = 'block';
        noResultsElement.style.display = 'none';

        // Show most recent completed tasks (limit to 5)
        const recentTasks = completedTasks.slice(0, 5);
        container.innerHTML = recentTasks.map(task => this.createCompletedTaskCard(task)).join('');

        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    createCompletedTaskCard(task) {
        const duration = task.endTime && task.startTime ?
            ((new Date(task.endTime) - new Date(task.startTime)) / 1000).toFixed(1) : 'N/A';

        const hasResult = task.result && (task.result.data || task.result.answer);

        return `
            <div class="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:bg-white/10 transition-colors">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i data-lucide="check-circle" class="w-5 h-5 text-green-400 mr-2"></i>
                        <h4 class="text-white font-medium">${task.name}</h4>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-white/60 text-sm">${duration}s</span>
                        ${hasResult ? `
                            <button onclick="app.showTaskResult('${task.id}')"
                                    class="text-blue-400 hover:text-blue-300 text-sm">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>

                ${task.result && task.result.answer ? `
                    <p class="text-white/70 text-sm mb-2 line-clamp-2">${task.result.answer}</p>
                ` : ''}

                ${task.result && task.result.data && task.result.data.length > 0 ? `
                    <div class="flex items-center text-white/60 text-xs">
                        <i data-lucide="database" class="w-3 h-3 mr-1"></i>
                        <span>${task.result.data.length} rows returned</span>
                    </div>
                ` : ''}
            </div>
        `;
    }

    updateTaskStatistics(allTasks, completedTasks, failedTasks) {
        // Total tasks
        const totalElement = document.getElementById('total-tasks-stat');
        if (totalElement) {
            totalElement.textContent = allTasks.length;
        }

        // Success rate
        const successRateElement = document.getElementById('success-rate-stat');
        if (successRateElement) {
            const totalFinished = completedTasks.length + failedTasks.length;
            const successRate = totalFinished > 0 ? (completedTasks.length / totalFinished * 100).toFixed(0) : 100;
            successRateElement.textContent = `${successRate}%`;
        }

        // Average time
        const avgTimeElement = document.getElementById('avg-time-stat');
        if (avgTimeElement) {
            if (completedTasks.length > 0) {
                const totalTime = completedTasks.reduce((sum, task) => {
                    if (task.endTime && task.startTime) {
                        return sum + (new Date(task.endTime) - new Date(task.startTime));
                    }
                    return sum;
                }, 0);
                const avgTime = (totalTime / completedTasks.length / 1000).toFixed(1);
                avgTimeElement.textContent = `${avgTime}s`;
            } else {
                avgTimeElement.textContent = '0s';
            }
        }
    }

    getTaskStatusIcon(status) {
        const icons = {
            'pending': 'clock',
            'planning': 'brain',
            'executing': 'loader',
            'completed': 'check-circle',
            'failed': 'x-circle',
            'cancelled': 'minus-circle'
        };
        return icons[status] || 'help-circle';
    }

    getTaskStatusColor(status) {
        const colors = {
            'pending': 'text-yellow-400',
            'planning': 'text-purple-400',
            'executing': 'text-blue-400',
            'completed': 'text-green-400',
            'failed': 'text-red-400',
            'cancelled': 'text-gray-400'
        };
        return colors[status] || 'text-gray-400';
    }

    showTaskResult(taskId) {
        const task = taskManager.getTask(taskId);
        if (task && task.result) {
            console.log('📋 Showing task result:', task.result);

            // Add to results if not already there
            const existingIndex = this.results.findIndex(r =>
                r.question === task.result.question ||
                r.sql === task.result.sql
            );

            if (existingIndex === -1) {
                this.results.unshift(task.result);
            }

            // Switch to AI Chat tab and display result
            this.switchTab('ai-chat');
            displayResults(this.results);
        }
    }

    async executeSQLQuery() {
        console.log('executeSQLQuery called');

        const sql = document.getElementById('sql-input').value.trim();
        if (!sql) {
            showError('Please enter a SQL query');
            return;
        }

        const exportFormat = document.getElementById('export-format').value;

        try {
            // Create async task on server
            const taskId = await taskManager.createSQLExecuteTask(sql, exportFormat || null);

            console.log('SQL execution task created:', taskId);
            return taskId;

        } catch (error) {
            console.error('Error creating SQL execution task:', error);
            showError(`Failed to create SQL task: ${error.message}`);
        }
    }

    handleSQLResult(result) {
        // Display SQL results
        displaySQLResults(result);
        console.log('SQL execution result processed');
    }

    displayChart(chartData) {
        if (!chartData || !chartData.success) {
            return;
        }

        const chartContainer = document.getElementById('chart-container');
        chartContainer.innerHTML = '';

        // Show chart section
        document.getElementById('chart-section').style.display = 'block';

        // Display chart type and info
        const chartInfo = document.createElement('div');
        chartInfo.className = 'mb-4 p-3 bg-blue-50 rounded-lg';
        chartInfo.innerHTML = `
            <p class="text-sm text-blue-800">
                <strong>Chart Type:</strong> ${chartData.chart_type.toUpperCase()}
                <span class="text-blue-600">(${chartData.analysis.reason})</span>
            </p>
            <p class="text-xs text-blue-600 mt-1">
                Data: ${chartData.data_summary.rows} rows, ${chartData.data_summary.columns} columns
            </p>
        `;
        chartContainer.appendChild(chartInfo);

        // Display matplotlib chart if available
        if (chartData.matplotlib_chart) {
            const imgElement = document.createElement('div');
            imgElement.className = 'mb-4';
            imgElement.innerHTML = `
                <h4 class="text-md font-medium mb-2">📈 Static Chart</h4>
                <img src="${chartData.matplotlib_chart}"
                     alt="Data Visualization"
                     class="max-w-full h-auto mx-auto border rounded-lg shadow-sm"
                     style="max-height: 500px;">
            `;
            chartContainer.appendChild(imgElement);
        }

        // Display plotly chart if available
        if (chartData.plotly_chart) {
            const plotlyDiv = document.createElement('div');
            plotlyDiv.className = 'mb-4';
            plotlyDiv.innerHTML = `
                <h4 class="text-md font-medium mb-2">📊 Interactive Chart</h4>
                <div id="plotly-chart-${Date.now()}" class="border rounded-lg" style="height: 400px;">
                    <div class="flex items-center justify-center h-full bg-gray-50 text-gray-500">
                        <p>📊 Interactive chart data available (Plotly.js integration needed)</p>
                    </div>
                </div>
            `;
            chartContainer.appendChild(plotlyDiv);
        }

        // Scroll to chart section
        document.getElementById('chart-section').scrollIntoView({ behavior: 'smooth' });
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new DataAnalysisApp();
    window.app.init();
});

// Export for global access
window.DataAnalysisApp = DataAnalysisApp;
