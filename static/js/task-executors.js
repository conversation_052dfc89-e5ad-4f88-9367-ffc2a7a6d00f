// Task Executors for Different Operations
class TaskExecutors {
    
    // AI Query Task Executor
    static async executeAIQuery(updateProgress, task) {
        const { question, options } = task.metadata;
        
        updateProgress(10, 'Preparing query...');
        
        try {
            updateProgress(20, 'Sending to AI...');
            
            const response = await fetch('/api/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    question: question,
                    include_raw_data: options.includeRawData || false,
                    session_id: options.sessionId || null,
                    optimize_prompt: options.optimizePrompt || false,
                    enable_task_decomposition: options.enableTaskDecomposition || false,
                    generate_charts: options.generateCharts || false
                })
            });

            updateProgress(60, 'Processing response...');

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Server error (${response.status}): ${errorText}`);
            }

            const result = await response.json();
            
            updateProgress(90, 'Finalizing...');
            
            // Store the question with the result
            result.question = question;
            
            updateProgress(100, 'Complete!');
            
            return result;
            
        } catch (error) {
            throw new Error(`AI Query failed: ${error.message}`);
        }
    }

    // SQL Execution Task Executor
    static async executeSQLQuery(updateProgress, task) {
        const { sql, exportFormat } = task.metadata;
        
        updateProgress(10, 'Validating SQL...');
        
        try {
            updateProgress(20, 'Executing query...');
            
            const response = await fetch('/api/execute-sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sql: sql,
                    export_format: exportFormat || null
                })
            });

            updateProgress(70, 'Processing results...');

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `Server error (${response.status})`);
            }

            const result = await response.json();
            
            updateProgress(90, 'Preparing output...');
            
            // Handle export if requested
            if (result.export_data && result.export_filename) {
                updateProgress(95, 'Generating export...');
                TaskExecutors.downloadFile(result.export_data, result.export_filename, exportFormat);
            }
            
            updateProgress(100, 'Query executed successfully!');
            
            return result;
            
        } catch (error) {
            throw new Error(`SQL execution failed: ${error.message}`);
        }
    }

    // Database Info Loading Task Executor
    static async loadDatabaseInfo(updateProgress, task) {
        updateProgress(10, 'Connecting to database...');
        
        try {
            updateProgress(30, 'Fetching table information...');
            
            const response = await fetch('/api/database-info');
            
            updateProgress(70, 'Processing schema...');
            
            if (!response.ok) {
                throw new Error(`Failed to load database info: ${response.status}`);
            }
            
            const dbInfo = await response.json();
            
            updateProgress(90, 'Updating interface...');
            
            // Update the app's database info
            if (window.app) {
                window.app.dbInfo = dbInfo;
                window.app.displayDatabaseInfo();
            }
            
            updateProgress(100, 'Database info loaded!');
            
            return dbInfo;
            
        } catch (error) {
            throw new Error(`Database info loading failed: ${error.message}`);
        }
    }

    // Data Export Task Executor
    static async exportData(updateProgress, task) {
        const { data, format, filename } = task.metadata;
        
        updateProgress(10, 'Preparing data...');
        
        try {
            updateProgress(30, 'Converting format...');
            
            let content = '';
            let mimeType = '';
            
            if (format === 'csv') {
                content = TaskExecutors.convertToCSV(data);
                mimeType = 'text/csv';
            } else if (format === 'json') {
                content = JSON.stringify(data, null, 2);
                mimeType = 'application/json';
            } else {
                throw new Error(`Unsupported export format: ${format}`);
            }
            
            updateProgress(70, 'Generating file...');
            
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const finalFilename = filename || `export_${timestamp}.${format}`;
            
            updateProgress(90, 'Starting download...');
            
            TaskExecutors.downloadFile(content, finalFilename, format);
            
            updateProgress(100, 'Export complete!');
            
            return { filename: finalFilename, size: content.length };
            
        } catch (error) {
            throw new Error(`Data export failed: ${error.message}`);
        }
    }

    // Batch Query Task Executor
    static async executeBatchQueries(updateProgress, task) {
        const { queries } = task.metadata;
        const results = [];
        
        updateProgress(5, 'Starting batch execution...');
        
        try {
            for (let i = 0; i < queries.length; i++) {
                const query = queries[i];
                const progress = 10 + (i / queries.length) * 80;
                
                updateProgress(progress, `Executing query ${i + 1} of ${queries.length}...`);
                
                const response = await fetch('/api/execute-sql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ sql: query })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Query ${i + 1} failed: ${errorData.detail}`);
                }

                const result = await response.json();
                results.push({
                    query: query,
                    result: result,
                    index: i + 1
                });
            }
            
            updateProgress(95, 'Finalizing results...');
            
            updateProgress(100, `Batch execution complete! ${queries.length} queries executed.`);
            
            return {
                totalQueries: queries.length,
                results: results,
                summary: `Successfully executed ${queries.length} queries`
            };
            
        } catch (error) {
            throw new Error(`Batch execution failed: ${error.message}`);
        }
    }

    // File Upload Task Executor
    static async uploadFile(updateProgress, task) {
        const { file, endpoint } = task.metadata;
        
        updateProgress(5, 'Preparing upload...');
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            updateProgress(10, 'Starting upload...');
            
            const xhr = new XMLHttpRequest();
            
            return new Promise((resolve, reject) => {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const progress = 10 + (e.loaded / e.total) * 80;
                        updateProgress(progress, `Uploading... ${Math.round(progress)}%`);
                    }
                });
                
                xhr.addEventListener('load', () => {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        updateProgress(95, 'Processing upload...');
                        
                        try {
                            const result = JSON.parse(xhr.responseText);
                            updateProgress(100, 'Upload complete!');
                            resolve(result);
                        } catch (error) {
                            reject(new Error('Invalid response format'));
                        }
                    } else {
                        reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
                    }
                });
                
                xhr.addEventListener('error', () => {
                    reject(new Error('Upload failed: Network error'));
                });
                
                xhr.open('POST', endpoint || '/api/upload');
                xhr.send(formData);
            });
            
        } catch (error) {
            throw new Error(`File upload failed: ${error.message}`);
        }
    }

    // Utility: Convert data to CSV
    static convertToCSV(data) {
        if (!data || !Array.isArray(data) || data.length === 0) {
            return '';
        }
        
        // Get headers from first object
        const headers = Object.keys(data[0]);
        
        // Create CSV content
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    // Escape quotes and wrap in quotes if contains comma or quote
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }

    // Utility: Download file
    static downloadFile(content, filename, format) {
        const mimeTypes = {
            'csv': 'text/csv',
            'json': 'application/json',
            'txt': 'text/plain'
        };
        
        const mimeType = mimeTypes[format] || 'application/octet-stream';
        
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    // Utility: Delay function for testing
    static async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Demo Task Executor (for testing)
    static async demoTask(updateProgress, task) {
        const { duration = 5000, steps = 10 } = task.metadata;
        const stepDuration = duration / steps;
        
        for (let i = 0; i <= steps; i++) {
            const progress = (i / steps) * 100;
            updateProgress(progress, `Step ${i} of ${steps}...`);
            
            if (i < steps) {
                await TaskExecutors.delay(stepDuration);
            }
        }
        
        return { message: 'Demo task completed successfully!', steps: steps };
    }
}

// Export for global use
window.TaskExecutors = TaskExecutors;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskExecutors;
}
