import clickhouse_connect
import pandas as pd
from typing import List, Dict, Any, Optional
import logging
import threading
from config import CLICKHOUSE_CONFIG

logger = logging.getLogger(__name__)

class ClickHouseClient:
    """ClickHouse database client for AutoGen agents."""
    
    def __init__(self):
        """Initialize ClickHouse client with configuration."""
        self.config = CLICKHOUSE_CONFIG
        self.client = None
        self._lock = threading.Lock()  # Thread safety for concurrent queries
        self.connect()
    
    def connect(self):
        """Establish connection to ClickHouse."""
        try:
            self.client = clickhouse_connect.get_client(
                host=self.config["host"],
                port=self.config["port"],
                username=self.config["user"],
                password=self.config["password"],
                database=self.config["database"],
                secure=self.config["secure"]
            )
            logger.info("Successfully connected to ClickHouse")
        except Exception as e:
            logger.error(f"Failed to connect to ClickHouse: {e}")
            raise
    
    def execute_query(self, query: str) -> pd.DataFrame:
        """Execute a SELECT query and return results as DataFrame."""
        with self._lock:  # Ensure thread safety
            try:
                result = self.client.query_df(query)
                logger.info(f"Query executed successfully. Returned {len(result)} rows.")
                return result
            except Exception as e:
                logger.error(f"Query execution failed: {e}")
                raise
    
    def execute_command(self, command: str) -> bool:
        """Execute a non-SELECT command (CREATE, INSERT, etc.)."""
        try:
            self.client.command(command)
            logger.info("Command executed successfully")
            return True
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            raise
    
    def get_table_schema(self, table_name: str) -> List[Dict[str, Any]]:
        """Get schema information for a table."""
        query = f"""
        SELECT 
            name,
            type,
            default_kind,
            default_expression,
            comment
        FROM system.columns 
        WHERE database = '{self.config["database"]}' 
        AND table = '{table_name}'
        ORDER BY position
        """
        return self.execute_query(query).to_dict('records')
    
    def get_tables(self) -> List[str]:
        """Get list of tables in the database."""
        query = f"""
        SELECT name 
        FROM system.tables 
        WHERE database = '{self.config["database"]}'
        """
        result = self.execute_query(query)
        return result['name'].tolist()
    
    def get_table_sample(self, table_name: str, limit: int = 5) -> pd.DataFrame:
        """Get a sample of data from a table."""
        query = f"SELECT * FROM {table_name} LIMIT {limit}"
        return self.execute_query(query)
    
    def close(self):
        """Close the database connection."""
        if self.client:
            self.client.close()
            logger.info("ClickHouse connection closed")

# Global client instance
clickhouse_client = ClickHouseClient()
