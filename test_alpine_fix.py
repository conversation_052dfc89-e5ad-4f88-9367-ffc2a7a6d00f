#!/usr/bin/env python3
"""
Quick test to check if Alpine.js submitQuery function is working
"""

import asyncio
from playwright.async_api import async_playwright

async def test_alpine_fix():
    """Test if the Alpine.js submitQuery function is accessible."""
    
    async with async_playwright() as p:
        print("🧪 Testing Alpine.js submitQuery fix...")
        
        # Launch browser
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # Enable console logging
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"Console: {msg.text}"))
        page.on("pageerror", lambda error: console_messages.append(f"Error: {error}"))
        
        try:
            # Navigate to the page
            print("📱 Loading page...")
            await page.goto("http://localhost:8000")
            
            # Wait for page to load
            await page.wait_for_selector("h1", timeout=10000)
            print("✅ Page loaded")
            
            # Wait for Alpine.js to initialize
            await page.wait_for_timeout(3000)
            
            # Check if Alpine.js is loaded
            alpine_loaded = await page.evaluate("() => typeof window.Alpine !== 'undefined'")
            print(f"Alpine.js loaded: {alpine_loaded}")
            
            # Check if queryApp function exists
            query_app_exists = await page.evaluate("() => typeof queryApp === 'function'")
            print(f"queryApp function exists: {query_app_exists}")
            
            # Check if the Alpine.js data is initialized
            alpine_data_exists = await page.evaluate("""
                () => {
                    const container = document.querySelector('[x-data]');
                    return container && container._x_dataStack && container._x_dataStack.length > 0;
                }
            """)
            print(f"Alpine.js data initialized: {alpine_data_exists}")
            
            # Try to access submitQuery function through Alpine.js
            submit_query_accessible = await page.evaluate("""
                () => {
                    try {
                        const container = document.querySelector('[x-data]');
                        if (container && container._x_dataStack && container._x_dataStack[0]) {
                            return typeof container._x_dataStack[0].submitQuery === 'function';
                        }
                        return false;
                    } catch (e) {
                        return false;
                    }
                }
            """)
            print(f"submitQuery function accessible: {submit_query_accessible}")
            
            # Try clicking the button to see if it works
            print("🖱️ Testing button click...")
            
            # Fill in a test question
            await page.fill("input[placeholder*='Ask a question']", "test question")
            
            # Try clicking the button
            try:
                await page.click("button:has-text('Ask')", timeout=5000)
                print("✅ Button click successful")
                
                # Wait a moment to see if any errors occur
                await page.wait_for_timeout(2000)
                
            except Exception as e:
                print(f"❌ Button click failed: {e}")
            
            # Print all console messages
            print("\n📝 Console messages:")
            for msg in console_messages:
                print(f"  {msg}")
            
            # Keep browser open for inspection
            print("⏳ Keeping browser open for 10 seconds...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"❌ Test error: {e}")
            
        finally:
            await browser.close()
            print("🎭 Browser closed")

if __name__ == "__main__":
    asyncio.run(test_alpine_fix())
