#!/usr/bin/env python3
"""
Test the jQuery implementation
"""

import asyncio
from playwright.async_api import async_playwright

async def test_jquery_implementation():
    """Test if the jQuery implementation works correctly."""
    
    async with async_playwright() as p:
        print("🧪 Testing jQuery implementation...")
        
        # Launch browser
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # Enable console logging
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"Console: {msg.text}"))
        page.on("pageerror", lambda error: console_messages.append(f"Error: {error}"))
        
        try:
            # Navigate to the page
            print("📱 Loading page...")
            await page.goto("http://localhost:8000")
            
            # Wait for page to load
            await page.wait_for_selector("h1", timeout=10000)
            print("✅ Page loaded")
            
            # Wait for jQuery to load and initialize
            await page.wait_for_timeout(3000)
            
            # Check if jQuery is loaded
            jquery_loaded = await page.evaluate("() => typeof $ !== 'undefined'")
            print(f"jQuery loaded: {jquery_loaded}")
            
            # Check if submitQuery function exists
            submit_query_exists = await page.evaluate("() => typeof submitQuery === 'function'")
            print(f"submitQuery function exists: {submit_query_exists}")
            
            # Check if database info is displayed
            db_info_visible = await page.is_visible("#db-info")
            print(f"Database info visible: {db_info_visible}")
            
            # Check if example questions are populated
            example_questions = await page.query_selector_all(".example-question")
            print(f"Example questions count: {len(example_questions)}")
            
            # Test clicking an example question
            if example_questions:
                print("🖱️ Testing example question click...")
                await example_questions[0].click()
                
                # Check if input was filled
                input_value = await page.input_value("#question-input")
                print(f"Input filled with: '{input_value}'")
            
            # Test manual input and button click
            print("🖱️ Testing manual input and button click...")
            
            # Clear and fill input
            await page.fill("#question-input", "What tables do we have?")
            
            # Check if button is enabled
            button_disabled = await page.is_disabled("#ask-button")
            print(f"Ask button disabled: {button_disabled}")
            
            # Click the button
            try:
                await page.click("#ask-button", timeout=5000)
                print("✅ Button click successful")
                
                # Wait for loading state
                await page.wait_for_timeout(2000)
                
                # Check if loading state is shown
                loading_visible = await page.is_visible("#loading-state")
                print(f"Loading state visible: {loading_visible}")
                
                # Wait for response (up to 30 seconds)
                print("⏳ Waiting for response...")
                for i in range(30):
                    await page.wait_for_timeout(1000)
                    
                    # Check if results appeared
                    results_visible = await page.is_visible("#results-container")
                    error_visible = await page.is_visible("#error-state")
                    
                    if results_visible:
                        print(f"✅ Results appeared after {i+1} seconds")
                        break
                    elif error_visible:
                        error_text = await page.text_content("#error-message")
                        print(f"❌ Error appeared: {error_text}")
                        break
                    elif i % 5 == 0:
                        print(f"⏳ Still waiting... ({i+1}/30 seconds)")
                
            except Exception as e:
                print(f"❌ Button click failed: {e}")
            
            # Print all console messages
            print("\n📝 Console messages:")
            for msg in console_messages[-10:]:  # Show last 10 messages
                print(f"  {msg}")
            
            # Keep browser open for inspection
            print("⏳ Keeping browser open for 10 seconds...")
            await page.wait_for_timeout(10000)
            
        except Exception as e:
            print(f"❌ Test error: {e}")
            
        finally:
            await browser.close()
            print("🎭 Browser closed")

if __name__ == "__main__":
    asyncio.run(test_jquery_implementation())
