#!/usr/bin/env python3
"""
AutoGen + ClickHouse Web Service
Provides a web interface for AI-powered database queries
"""

import asyncio
import json
import logging
import uuid
import time
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor
from enum import Enum

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.responses import H<PERSON>LResponse, JSONResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Import our modules
from clickhouse_client import clickhouse_client
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo
from config import LLM_CONFIG
from chart_generator import chart_generator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Task Status Enum
class TaskStatus(str, Enum):
    PENDING = "pending"
    PLANNING = "planning"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# Global task storage and WebSocket connections
tasks_storage = {}
active_connections = {}
executor = ThreadPoolExecutor(max_workers=3)

class TaskStep:
    def __init__(self, name: str, description: str, estimated_duration: float = 1.0):
        self.name = name
        self.description = description
        self.estimated_duration = estimated_duration
        self.status = TaskStatus.PENDING
        self.start_time = None
        self.end_time = None
        self.result = None
        self.error = None

class AsyncTask:
    def __init__(self, task_id: str, name: str, task_type: str, metadata: dict = None):
        self.task_id = task_id
        self.name = name
        self.task_type = task_type
        self.metadata = metadata or {}
        self.status = TaskStatus.PENDING
        self.steps = []
        self.current_step_index = 0
        self.progress = 0.0
        self.start_time = None
        self.end_time = None
        self.result = None
        self.error = None
        self.created_at = datetime.now()

    def add_step(self, step: TaskStep):
        self.steps.append(step)

    def get_current_step(self) -> Optional[TaskStep]:
        if 0 <= self.current_step_index < len(self.steps):
            return self.steps[self.current_step_index]
        return None

    def advance_step(self):
        if self.current_step_index < len(self.steps) - 1:
            self.current_step_index += 1
            return True
        return False

    def calculate_progress(self) -> float:
        if not self.steps:
            return 0.0

        completed_steps = sum(1 for step in self.steps[:self.current_step_index] if step.status == TaskStatus.COMPLETED)
        current_step = self.get_current_step()

        if current_step and current_step.status == TaskStatus.EXECUTING:
            # Estimate progress within current step
            step_progress = 0.5  # Assume 50% progress for executing step
            return (completed_steps + step_progress) / len(self.steps) * 100
        else:
            return completed_steps / len(self.steps) * 100

    def to_dict(self) -> dict:
        return {
            "task_id": self.task_id,
            "name": self.name,
            "task_type": self.task_type,
            "status": self.status,
            "progress": self.calculate_progress(),
            "current_step_index": self.current_step_index,
            "steps": [
                {
                    "name": step.name,
                    "description": step.description,
                    "status": step.status,
                    "start_time": step.start_time.isoformat() if step.start_time else None,
                    "end_time": step.end_time.isoformat() if step.end_time else None,
                    "error": str(step.error) if step.error else None
                }
                for step in self.steps
            ],
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "created_at": self.created_at.isoformat(),
            "result": self.result,
            "error": str(self.error) if self.error else None,
            "metadata": self.metadata
        }

# WebSocket Connection Manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")

    async def send_personal_message(self, message: dict, client_id: str):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)

    async def broadcast(self, message: dict):
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)

        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)

    async def send_task_update(self, task: AsyncTask, client_id: str = None):
        try:
            message = {
                "type": "task_update",
                "data": task.to_dict()
            }

            if client_id:
                await self.send_personal_message(message, client_id)
            else:
                await self.broadcast(message)
        except Exception as e:
            logger.error(f"Error sending task update: {e}")
            # Try to send a simplified version
            try:
                simplified_message = {
                    "type": "task_update",
                    "data": {
                        "task_id": task.task_id,
                        "name": task.name,
                        "status": task.status,
                        "progress": task.calculate_progress(),
                        "error": str(task.error) if task.error else None
                    }
                }

                if client_id:
                    await self.send_personal_message(simplified_message, client_id)
                else:
                    await self.broadcast(simplified_message)
            except Exception as e2:
                logger.error(f"Error sending simplified task update: {e2}")

manager = ConnectionManager()

# Task Executors with Step Decomposition
class TaskExecutor:
    @staticmethod
    async def execute_ai_query(task: AsyncTask):
        """Execute AI query with detailed steps and optional task decomposition"""
        question = task.metadata.get('question', '')
        options = task.metadata.get('options', {})

        # Check if task decomposition is enabled
        enable_task_decomposition = options.get('enable_task_decomposition', False)
        optimize_prompt = options.get('optimize_prompt', False)

        # Define base steps
        steps = []

        if optimize_prompt:
            steps.append(TaskStep("optimize_prompt", "优化问题表述", 1.0))

        if enable_task_decomposition:
            steps.extend([
                TaskStep("analyze_complexity", "分析问题复杂度", 1.0),
                TaskStep("decompose_task", "拆解复杂任务", 2.0),
                TaskStep("execute_subtasks", "执行子任务", 5.0),
                TaskStep("synthesize_results", "综合分析结果", 2.0)
            ])
        else:
            steps.extend([
                TaskStep("analyze_question", "分析问题并理解用户意图", 2.0),
                TaskStep("load_schema", "加载数据库架构信息", 1.0),
                TaskStep("generate_sql", "生成SQL查询语句", 3.0),
                TaskStep("execute_sql", "执行SQL查询", 2.0),
                TaskStep("analyze_results", "分析查询结果", 2.0)
            ])

        steps.append(TaskStep("generate_response", "生成最终回答", 1.0))

        for step in steps:
            task.add_step(step)

        task.status = TaskStatus.PLANNING
        await manager.send_task_update(task)

        try:
            task.status = TaskStatus.EXECUTING
            task.start_time = datetime.now()

            # Store variables for step execution
            processed_question = question
            sub_tasks = []
            db_info = None

            # Execute each step
            for i, step in enumerate(task.steps):
                task.current_step_index = i
                step.status = TaskStatus.EXECUTING
                step.start_time = datetime.now()

                await manager.send_task_update(task)

                # Execute different step types
                if step.name == "optimize_prompt":
                    await asyncio.sleep(0.8)
                    try:
                        db_info = get_database_info()
                        if "error" in db_info:
                            raise Exception(f"Database info error: {db_info['error']}")
                        processed_question = await optimize_prompt(question, db_info)
                        step.result = f"优化后的问题: {processed_question}"
                        task.metadata['processed_question'] = processed_question
                    except Exception as e:
                        step.result = f"问题优化失败: {str(e)}"
                        processed_question = question

                elif step.name == "analyze_complexity":
                    await asyncio.sleep(0.5)
                    # Simple complexity analysis
                    complexity_indicators = ['and', 'or', '以及', '还有', '同时', '分别', '对比', '比较']
                    is_complex = any(indicator in processed_question.lower() for indicator in complexity_indicators)
                    step.result = f"问题复杂度: {'复杂' if is_complex else '简单'}"

                elif step.name == "decompose_task":
                    await asyncio.sleep(1.5)
                    try:
                        if not db_info:
                            db_info = get_database_info()
                            if "error" in db_info:
                                raise Exception(f"Database info error: {db_info['error']}")
                        sub_tasks = await decompose_complex_task(processed_question, db_info)
                        step.result = f"任务拆解完成，共 {len(sub_tasks)} 个子任务"
                        task.metadata['sub_tasks'] = sub_tasks
                    except Exception as e:
                        step.result = f"任务拆解失败: {str(e)}"
                        sub_tasks = [processed_question]

                elif step.name == "execute_subtasks":
                    try:
                        if not db_info:
                            db_info = get_database_info()
                            if "error" in db_info:
                                raise Exception(f"Database info error: {db_info['error']}")

                        session_id = options.get('session_id', 'default')
                        enable_charts = options.get('generate_charts', False)

                        # Execute each sub-task with progress tracking
                        sub_results = []
                        total_subtasks = len(sub_tasks)

                        for j, sub_task in enumerate(sub_tasks, 1):
                            # Update step progress for current subtask
                            step.result = f"执行子任务 {j}/{total_subtasks}: {sub_task[:50]}..."
                            await manager.send_task_update(task)

                            # Check if subtask is complex and needs further decomposition
                            complexity_indicators = ['and', 'or', '以及', '还有', '同时', '分别', '对比', '比较', '分析', '显示']
                            is_complex_subtask = any(indicator in sub_task.lower() for indicator in complexity_indicators)

                            if is_complex_subtask and len(sub_task.split()) > 8:
                                # Further decompose complex subtask
                                try:
                                    nested_subtasks = await decompose_complex_task(sub_task, db_info)
                                    if len(nested_subtasks) > 1:
                                        # Execute nested subtasks
                                        nested_results = []
                                        for k, nested_task in enumerate(nested_subtasks, 1):
                                            step.result = f"执行子任务 {j}/{total_subtasks} - 嵌套任务 {k}/{len(nested_subtasks)}"
                                            await manager.send_task_update(task)

                                            analyst = await create_ai_analyst(db_info, session_id, enable_charts)

                                            # Create more specific prompt for nested task
                                            enhanced_prompt = f"""
                                            请执行以下具体的数据分析任务: {nested_task}

                                            重要要求:
                                            1. 必须使用execute_clickhouse_query工具执行SQL查询
                                            2. SQL查询必须使用英文列名，不要使用中文列名
                                            3. 只使用ClickHouse支持的标准SQL语法
                                            4. 避免使用PERCENTILE_CONT等高级函数，使用quantile()函数代替
                                            5. 分析查询结果并提供具体的数据洞察
                                            6. 避免询问用户问题，直接提供分析结果

                                            可用的表和字段:
                                            - logs表: timestamp, time_local, method, uri, status, remote_addr, response_time, http_user_agent, http_referer, body_bytes_sent, request_length, http_x_forwarded_for, server_protocol, request_id, host, request_headers, request_body
                                            """

                                            nested_response = await analyst.run(task=enhanced_prompt)
                                            nested_answer = nested_response.messages[-1].content
                                            nested_results.append({
                                                'task': nested_task,
                                                'answer': nested_answer
                                            })

                                            await asyncio.sleep(0.5)  # Brief pause between nested tasks

                                        # Combine nested results
                                        combined_answer = f"**复杂子任务分析** (拆解为 {len(nested_subtasks)} 个嵌套任务):\n\n"
                                        for k, nested_result in enumerate(nested_results, 1):
                                            combined_answer += f"**嵌套任务 {k}**: {nested_result['task']}\n\n{nested_result['answer']}\n\n"
                                            if k < len(nested_results):
                                                combined_answer += "-" * 40 + "\n\n"

                                        sub_results.append({
                                            'task': sub_task,
                                            'answer': combined_answer,
                                            'nested_tasks': nested_subtasks
                                        })
                                    else:
                                        # Execute as single task
                                        analyst = await create_ai_analyst(db_info, session_id, enable_charts)
                                        enhanced_prompt = f"""
                                        请执行以下数据分析任务: {sub_task}

                                        重要要求:
                                        1. 必须使用execute_clickhouse_query工具执行SQL查询
                                        2. SQL查询必须使用英文列名，不要使用中文列名
                                        3. 只使用ClickHouse支持的标准SQL语法
                                        4. 避免使用PERCENTILE_CONT等高级函数，使用quantile()函数代替
                                        5. 分析查询结果并提供具体的数据洞察
                                        6. 避免询问用户问题，直接提供分析结果

                                        可用的表和字段:
                                        - logs表: timestamp, time_local, method, uri, status, remote_addr, response_time, http_user_agent, http_referer, body_bytes_sent, request_length, http_x_forwarded_for, server_protocol, request_id, host, request_headers, request_body
                                        """
                                        sub_response = await analyst.run(task=enhanced_prompt)
                                        sub_answer = sub_response.messages[-1].content
                                        sub_results.append({
                                            'task': sub_task,
                                            'answer': sub_answer
                                        })
                                except Exception as nested_error:
                                    logger.error(f"Nested task decomposition failed: {nested_error}")
                                    # Fallback to single task execution
                                    analyst = await create_ai_analyst(db_info, session_id, enable_charts)
                                    enhanced_prompt = f"""
                                    请执行以下数据分析任务: {sub_task}

                                    重要要求:
                                    1. 必须使用execute_clickhouse_query工具执行SQL查询
                                    2. SQL查询必须使用英文列名，不要使用中文列名
                                    3. 只使用ClickHouse支持的标准SQL语法
                                    4. 避免使用PERCENTILE_CONT等高级函数，使用quantile()函数代替
                                    5. 分析查询结果并提供具体的数据洞察
                                    6. 避免询问用户问题，直接提供分析结果

                                    可用的表和字段:
                                    - logs表: timestamp, time_local, method, uri, status, remote_addr, response_time, http_user_agent, http_referer, body_bytes_sent, request_length, http_x_forwarded_for, server_protocol, request_id, host, request_headers, request_body
                                    """
                                    sub_response = await analyst.run(task=enhanced_prompt)
                                    sub_answer = sub_response.messages[-1].content
                                    sub_results.append({
                                        'task': sub_task,
                                        'answer': sub_answer
                                    })
                            else:
                                # Execute simple subtask
                                analyst = await create_ai_analyst(db_info, session_id, enable_charts)
                                enhanced_prompt = f"""
                                请执行以下数据分析任务: {sub_task}

                                重要要求:
                                1. 必须使用execute_clickhouse_query工具执行SQL查询
                                2. SQL查询必须使用英文列名，不要使用中文列名
                                3. 只使用ClickHouse支持的标准SQL语法
                                4. 避免使用PERCENTILE_CONT等高级函数，使用quantile()函数代替
                                5. 分析查询结果并提供具体的数据洞察
                                6. 避免询问用户问题，直接提供分析结果

                                可用的表和字段:
                                - logs表: timestamp, time_local, method, uri, status, remote_addr, response_time, http_user_agent, http_referer, body_bytes_sent, request_length, http_x_forwarded_for, server_protocol, request_id, host, request_headers, request_body
                                """
                                sub_response = await analyst.run(task=enhanced_prompt)
                                sub_answer = sub_response.messages[-1].content
                                sub_results.append({
                                    'task': sub_task,
                                    'answer': sub_answer
                                })

                            await asyncio.sleep(0.5)  # Brief pause between subtasks

                        step.result = f"成功执行 {len(sub_results)} 个子任务"
                        task.metadata['sub_results'] = sub_results
                    except Exception as e:
                        step.result = f"子任务执行失败: {str(e)}"
                        raise e

                elif step.name == "synthesize_results":
                    await asyncio.sleep(1.5)
                    try:
                        sub_results = task.metadata.get('sub_results', [])
                        if sub_results:
                            # Count total tasks including nested ones
                            total_tasks = 0
                            nested_count = 0
                            for result in sub_results:
                                total_tasks += 1
                                if result.get('nested_tasks'):
                                    nested_count += len(result['nested_tasks'])

                            # Create comprehensive summary
                            if nested_count > 0:
                                combined_answer = f"**复杂任务智能分析** (拆解为 {len(sub_results)} 个主任务，包含 {nested_count} 个嵌套子任务):\n\n"
                            else:
                                combined_answer = f"**复杂任务分析** (拆解为 {len(sub_results)} 个子任务):\n\n"

                            for i, result in enumerate(sub_results, 1):
                                combined_answer += f"**子任务 {i}**: {result['task']}\n\n"

                                # Clean up the answer to remove redundant information
                                answer = result['answer']

                                # Remove "Sample data from" lines that don't provide value
                                lines = answer.split('\n')
                                cleaned_lines = []
                                skip_next = False

                                for line in lines:
                                    if skip_next:
                                        skip_next = False
                                        continue
                                    if 'Sample data from' in line and ':' in line:
                                        skip_next = True  # Skip the next line which is usually empty
                                        continue
                                    if line.strip() and not line.strip().startswith('timestamp time_local method'):
                                        cleaned_lines.append(line)

                                cleaned_answer = '\n'.join(cleaned_lines)
                                combined_answer += f"{cleaned_answer}\n\n"
                                combined_answer += "="*50 + "\n\n"

                            # Add summary insights
                            combined_answer += "**综合分析总结**:\n\n"
                            combined_answer += f"本次分析共执行了 {len(sub_results)} 个主要分析任务"
                            if nested_count > 0:
                                combined_answer += f"，其中包含 {nested_count} 个深度分析子任务"
                            combined_answer += "。每个任务都提供了具体的数据查询和分析结果，为您的决策提供了全面的数据支持。"

                            task.metadata['final_answer'] = combined_answer
                            step.result = f"成功综合 {len(sub_results)} 个子任务结果"
                            if nested_count > 0:
                                step.result += f"（包含 {nested_count} 个嵌套任务）"
                        else:
                            step.result = "没有子任务结果需要综合"
                    except Exception as e:
                        step.result = f"结果综合失败: {str(e)}"

                elif step.name == "analyze_question":
                    await asyncio.sleep(1)
                    step.result = f"分析问题: {processed_question}"

                elif step.name == "load_schema":
                    await asyncio.sleep(0.5)
                    # Actually load schema
                    try:
                        if not db_info:
                            db_info = get_database_info()
                        schema_info = get_database_schema()
                        step.result = f"加载了数据库架构信息"
                        task.metadata['db_info'] = db_info
                    except Exception as e:
                        step.result = f"架构加载失败: {str(e)}"

                elif step.name == "generate_sql":
                    await asyncio.sleep(2)
                    # Actually generate SQL using AI (for non-decomposed tasks)
                    try:
                        if not db_info:
                            db_info = get_database_info()
                            if "error" in db_info:
                                raise Exception(f"Database info error: {db_info['error']}")
                        session_id = options.get('session_id', 'default')

                        # Create AI analyst
                        analyst = await create_ai_analyst(db_info, session_id)

                        # Ask AI to analyze the question and generate SQL
                        analysis_prompt = f"""
                        Analyze this question and generate the appropriate SQL query: "{processed_question}"

                        Instructions:
                        1. Understand what the user wants to know
                        2. Look at the available database tables and fields
                        3. Generate a SQL query that answers the question
                        4. Respond with ONLY the SQL query, no explanations

                        Question: {processed_question}

                        Generate the SQL query:
                        """

                        response = await analyst.run(task=analysis_prompt)
                        ai_response = response.messages[-1].content

                        # Extract SQL from response
                        sql_query = extract_sql_from_response(ai_response)

                        # Generate answer
                        answer_prompt = f"""
                        The user asked: "{processed_question}"
                        We will execute this SQL: {sql_query}

                        Provide a brief, friendly explanation of what this query will show.
                        """

                        answer_response = await analyst.run(task=answer_prompt)
                        answer = answer_response.messages[-1].content

                        sql_result = {
                            'sql': sql_query,
                            'answer': answer,
                            'explanation': f'Generated SQL to answer: {processed_question}'
                        }

                        step.result = f"生成SQL: {sql_query}"
                        task.metadata['generated_sql'] = sql_query
                        task.metadata['ai_analysis'] = sql_result

                    except Exception as e:
                        logger.error(f"AI SQL generation failed: {e}")
                        # Fallback to simple logic
                        fallback_result = fallback_sql_generation(processed_question, options)
                        step.result = f"使用备用逻辑生成SQL: {fallback_result.get('sql', 'SHOW TABLES')}"
                        task.metadata['generated_sql'] = fallback_result.get('sql', 'SHOW TABLES')
                        task.metadata['ai_analysis'] = fallback_result

                elif step.name == "execute_sql":
                    await asyncio.sleep(1)
                    # Execute the generated SQL
                    try:
                        sql = task.metadata.get('generated_sql')
                        if sql:
                            sql_result = await asyncio.get_event_loop().run_in_executor(
                                executor,
                                lambda: execute_sql_safely(sql)
                            )
                            step.result = f"查询返回 {len(sql_result.get('data', []))} 行数据"
                            task.metadata['sql_result'] = sql_result
                        else:
                            step.result = "没有SQL可执行"
                    except Exception as e:
                        step.result = f"SQL执行失败: {str(e)}"
                        raise e

                elif step.name == "analyze_results":
                    await asyncio.sleep(1.5)
                    # Analyze the results
                    step.result = "分析查询结果并提取关键信息"

                elif step.name == "generate_response":
                    await asyncio.sleep(0.5)
                    # Generate final response based on execution path

                    if enable_task_decomposition and task.metadata.get('final_answer'):
                        # Use the synthesized answer from task decomposition
                        final_result = {
                            'question': question,
                            'processed_question': processed_question,
                            'answer': task.metadata['final_answer'],
                            'sub_tasks': task.metadata.get('sub_tasks', []),
                            'execution_time': (datetime.now() - task.start_time).total_seconds(),
                            'timestamp': datetime.now().isoformat(),
                            'task_decomposition_used': True
                        }
                        step.result = "生成复杂任务分析回答"
                    else:
                        # Use traditional single-task response
                        ai_analysis = task.metadata.get('ai_analysis', {})
                        sql_result = task.metadata.get('sql_result', {})

                        final_result = {
                            'question': question,
                            'processed_question': processed_question,
                            'answer': ai_analysis.get('answer', '查询已完成'),
                            'sql': task.metadata.get('generated_sql'),
                            'data': sql_result.get('data', []),
                            'execution_time': (datetime.now() - task.start_time).total_seconds(),
                            'timestamp': datetime.now().isoformat(),
                            'task_decomposition_used': False
                        }
                        step.result = "生成标准查询回答"

                    task.result = final_result

                step.status = TaskStatus.COMPLETED
                step.end_time = datetime.now()
                await manager.send_task_update(task)

            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()

        except Exception as e:
            current_step = task.get_current_step()
            if current_step:
                current_step.status = TaskStatus.FAILED
                current_step.error = str(e)
                current_step.end_time = datetime.now()

            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.end_time = datetime.now()

        await manager.send_task_update(task)
        return task

    @staticmethod
    async def execute_sql_query_task(task: AsyncTask):
        """Execute SQL query with detailed steps"""
        sql = task.metadata.get('sql', '')
        export_format = task.metadata.get('export_format')

        # Define steps
        steps = [
            TaskStep("validate_sql", "验证SQL语法", 0.5),
            TaskStep("prepare_execution", "准备执行环境", 0.5),
            TaskStep("execute_query", "执行SQL查询", 3.0),
            TaskStep("process_results", "处理查询结果", 1.0),
            TaskStep("format_output", "格式化输出", 0.5)
        ]

        if export_format:
            steps.append(TaskStep("generate_export", f"生成{export_format.upper()}导出文件", 1.0))

        for step in steps:
            task.add_step(step)

        task.status = TaskStatus.PLANNING
        await manager.send_task_update(task)

        try:
            task.status = TaskStatus.EXECUTING
            task.start_time = datetime.now()

            # Execute each step
            for i, step in enumerate(task.steps):
                task.current_step_index = i
                step.status = TaskStatus.EXECUTING
                step.start_time = datetime.now()

                await manager.send_task_update(task)

                if step.name == "validate_sql":
                    await asyncio.sleep(0.3)
                    # Basic SQL validation
                    if not sql.strip():
                        raise Exception("SQL查询不能为空")
                    if not sql.strip().upper().startswith('SELECT'):
                        raise Exception("只允许执行SELECT查询")
                    step.result = "SQL语法验证通过"

                elif step.name == "prepare_execution":
                    await asyncio.sleep(0.2)
                    step.result = "执行环境准备完成"

                elif step.name == "execute_query":
                    await asyncio.sleep(1)
                    # Actually execute SQL
                    try:
                        result = await asyncio.get_event_loop().run_in_executor(
                            executor,
                            lambda: execute_sql_safely(sql)
                        )
                        task.metadata['query_result'] = result
                        step.result = f"查询成功，返回 {len(result.get('data', []))} 行数据"
                    except Exception as e:
                        step.result = f"查询执行失败: {str(e)}"
                        raise e

                elif step.name == "process_results":
                    await asyncio.sleep(0.5)
                    result = task.metadata.get('query_result', {})
                    step.result = f"处理了 {len(result.get('data', []))} 行数据"

                elif step.name == "format_output":
                    await asyncio.sleep(0.3)
                    step.result = "输出格式化完成"

                elif step.name == "generate_export":
                    await asyncio.sleep(0.8)
                    # Generate export file
                    result = task.metadata.get('query_result', {})
                    if export_format and result.get('data'):
                        export_data = generate_export_data(result['data'], export_format)
                        task.metadata['export_data'] = export_data
                        task.metadata['export_filename'] = f"query_result_{int(time.time())}.{export_format}"
                        step.result = f"生成{export_format.upper()}导出文件"
                    else:
                        step.result = "跳过导出文件生成"

                step.status = TaskStatus.COMPLETED
                step.end_time = datetime.now()
                await manager.send_task_update(task)

            # Prepare final result
            query_result = task.metadata.get('query_result', {})
            final_result = {
                'sql': sql,
                'data': query_result.get('data', []),
                'columns': query_result.get('columns', []),
                'row_count': len(query_result.get('data', [])),
                'execution_time': (datetime.now() - task.start_time).total_seconds(),
                'message': '查询执行成功'
            }

            if task.metadata.get('export_data'):
                final_result['export_data'] = task.metadata['export_data']
                final_result['export_filename'] = task.metadata['export_filename']

            task.result = final_result
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()

        except Exception as e:
            current_step = task.get_current_step()
            if current_step:
                current_step.status = TaskStatus.FAILED
                current_step.error = str(e)
                current_step.end_time = datetime.now()

            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.end_time = datetime.now()

        await manager.send_task_update(task)
        return task

# Helper functions for task execution
def execute_sql_safely(sql: str) -> dict:
    """Safely execute SQL query with validation."""
    try:
        # Use existing SQL execution logic
        client = clickhouse_client

        # Execute query using the correct method
        df_result = client.execute_query(sql)

        # Convert DataFrame to list of dictionaries
        formatted_data = df_result.to_dict('records')

        # Get column names
        columns = df_result.columns.tolist()

        return {
            'data': formatted_data,
            'columns': columns,
            'row_count': len(formatted_data)
        }

    except Exception as e:
        logger.error(f"SQL execution error: {e}")
        raise e

def generate_export_data(data: list, format: str) -> str:
    """Generate export data in specified format."""
    if format.lower() == 'csv':
        if not data:
            return ''

        import csv
        import io

        output = io.StringIO()
        if data:
            fieldnames = data[0].keys()
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)

        return output.getvalue()

    elif format.lower() == 'json':
        return json.dumps(data, indent=2, default=str)

    else:
        raise ValueError(f"Unsupported export format: {format}")

def analyze_question_with_ai(question: str, options: dict) -> dict:
    """Analyze question using AI and generate SQL."""
    try:
        # Use the real AI analyst for SQL generation
        import asyncio

        # Get database info
        db_info = get_database_schema()
        session_id = options.get('session_id', 'default')

        # Create AI analyst
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            analyst = loop.run_until_complete(create_ai_analyst(db_info, session_id))

            # Ask the AI to generate SQL for the question
            sql_prompt = f"""
            Please analyze this question and generate the appropriate SQL query: "{question}"

            Requirements:
            1. Generate ONLY the SQL query, no explanations
            2. Use the exact field names from the database schema
            3. Make sure the query answers the specific question asked
            4. Return the SQL query in a clear format

            Question: {question}
            """

            response = loop.run_until_complete(analyst.run(task=sql_prompt))
            ai_response = response.messages[-1].content

            # Extract SQL from AI response
            sql_query = extract_sql_from_response(ai_response)

            # Generate a proper answer
            answer_prompt = f"""
            Based on the question "{question}" and the SQL query "{sql_query}",
            provide a brief explanation of what this query will show.
            Keep it concise and user-friendly.
            """

            answer_response = loop.run_until_complete(analyst.run(task=answer_prompt))
            answer = answer_response.messages[-1].content

            return {
                'sql': sql_query,
                'explanation': f'Generated SQL query to answer: {question}',
                'answer': answer
            }

        finally:
            loop.close()

    except Exception as e:
        logger.error(f"AI analysis error: {e}")
        # Fallback to simple logic
        return fallback_sql_generation(question, options)

def extract_sql_from_response(response: str) -> str:
    """Extract SQL query from AI response."""
    import re

    # Look for SQL in code blocks
    sql_pattern = r'```(?:sql)?\s*(.*?)\s*```'
    matches = re.findall(sql_pattern, response, re.DOTALL | re.IGNORECASE)

    if matches:
        return matches[0].strip()

    # Look for SELECT statements
    select_pattern = r'(SELECT\s+.*?)(?:\n\n|\Z)'
    matches = re.findall(select_pattern, response, re.DOTALL | re.IGNORECASE)

    if matches:
        return matches[0].strip()

    # Look for SHOW statements
    show_pattern = r'(SHOW\s+.*?)(?:\n|\Z)'
    matches = re.findall(show_pattern, response, re.IGNORECASE)

    if matches:
        return matches[0].strip()

    # Default fallback
    return "SHOW TABLES"

def fallback_sql_generation(question: str, options: dict) -> dict:
    """Fallback SQL generation when AI fails."""
    question_lower = question.lower()

    if 'table' in question_lower and ('what' in question_lower or 'show' in question_lower or 'list' in question_lower):
        return {
            'sql': "SHOW TABLES",
            'explanation': "Showing all available tables",
            'answer': "Here are the tables in your database"
        }
    elif 'products' in question_lower and ('类型' in question_lower or 'type' in question_lower):
        return {
            'sql': "SELECT DISTINCT category, COUNT(*) as count FROM products GROUP BY category ORDER BY count DESC",
            'explanation': "Analyzing product types",
            'answer': "Here are the different product types and their counts"
        }
    else:
        return {
            'sql': "SHOW TABLES",
            'explanation': "Showing available tables to help answer your question",
            'answer': f"I need to understand your question better. Here are the available tables that might help answer: '{question}'"
        }

def process_query_sync(question: str, options: dict) -> dict:
    """Synchronous version of query processing for task execution."""
    try:
        # Generate appropriate SQL based on the question
        question_lower = question.lower()

        # More intelligent SQL generation based on question content
        if 'table' in question_lower and ('what' in question_lower or 'show' in question_lower or 'list' in question_lower):
            # Questions about tables
            sql = "SHOW TABLES"
            answer = "Here are the tables in your database"
            explanation = "Showing all available tables in the database"

        elif 'schema' in question_lower or 'structure' in question_lower:
            # Questions about database structure
            sql = "SELECT table_name, table_comment FROM information_schema.tables WHERE table_schema = database()"
            answer = "Here is the database schema information"
            explanation = "Retrieving table structure and metadata"

        elif 'count' in question_lower and 'record' in question_lower:
            # Questions about record counts
            sql = "SELECT table_name, table_rows FROM information_schema.tables WHERE table_schema = database() AND table_rows > 0"
            answer = "Here are the record counts for each table"
            explanation = "Counting records in all tables"

        elif 'products' in question_lower and ('类型' in question_lower or 'type' in question_lower):
            # Questions about product types
            sql = "SELECT DISTINCT type, COUNT(*) as count FROM products GROUP BY type ORDER BY count DESC"
            answer = "Here are the different product types and their counts"
            explanation = "Analyzing product types in the products table"

        elif 'products' in question_lower and ('多少' in question_lower or 'count' in question_lower or 'how many' in question_lower):
            # Questions about product counts
            sql = "SELECT COUNT(*) as total_products FROM products"
            answer = "Here is the total count of products"
            explanation = "Counting total products in the products table"

        elif 'customer' in question_lower and ('demographic' in question_lower or '人口' in question_lower):
            # Questions about customer demographics
            sql = "SELECT age_group, gender, COUNT(*) as count FROM customers GROUP BY age_group, gender ORDER BY count DESC"
            answer = "Here are the customer demographics"
            explanation = "Analyzing customer demographics by age group and gender"

        elif 'sales' in question_lower and ('performance' in question_lower or '业绩' in question_lower or '表现' in question_lower):
            # Questions about sales performance
            sql = "SELECT DATE_FORMAT(sale_date, '%Y-%m') as month, SUM(amount) as total_sales, COUNT(*) as transaction_count FROM sales GROUP BY month ORDER BY month DESC LIMIT 12"
            answer = "Here is the sales performance data"
            explanation = "Analyzing monthly sales performance"

        elif any(table in question_lower for table in ['customers', 'sales', 'products', 'traffic_logs_with_geo']):
            # Questions mentioning specific tables - try to analyze the table
            table_name = None
            for table in ['customers', 'sales', 'products', 'traffic_logs_with_geo']:
                if table in question_lower:
                    table_name = table
                    break

            if table_name:
                sql = f"SELECT * FROM {table_name} LIMIT 10"
                answer = f"Here is a sample of data from the {table_name} table"
                explanation = f"Showing sample data from {table_name} table"
            else:
                sql = "SHOW TABLES"
                answer = "Here are the available tables"
                explanation = "Showing available tables"

        else:
            # For complex questions, try to provide helpful information
            sql = "SHOW TABLES"
            answer = f"I need to understand your question better. Here are the available tables that might help answer: '{question}'"
            explanation = "Showing available tables to help formulate a more specific query"

        return {
            'answer': answer,
            'sql_queries': [sql],
            'explanation': explanation
        }
    except Exception as e:
        logger.error(f"Query processing error: {e}")
        raise e

# Conversation history management
conversation_histories = {}  # session_id -> list of messages
MAX_HISTORY_LENGTH = 20  # Maximum number of messages to keep in history

# Chart data storage
last_generated_chart = None  # Store the last generated chart data
session_charts = {}  # session_id -> list of chart data

# Query execution tracking
executed_queries = []  # Store executed queries for current session

# Database tool functions for AI agent
def execute_clickhouse_query(query: str, generate_chart: bool = False) -> str:
    """
    Execute a ClickHouse query and return results as formatted string.

    Args:
        query: SQL query to execute
        generate_chart: Whether to generate chart for the results

    Returns:
        Formatted query results or error message
    """
    try:
        # Validate query (basic security check)
        query_lower = query.lower().strip()
        if not query_lower.startswith('select'):
            return "Error: Only SELECT queries are allowed for security reasons."

        # Show the SQL query being executed
        formatted_result = f"🔍 **执行的SQL查询:**\n```sql\n{query}\n```\n\n"

        # Store executed query for tracking
        global executed_queries
        executed_queries.append(query)

        # Execute query
        result_df = clickhouse_client.execute_query(query)

        if result_df.empty:
            formatted_result += "✅ 查询执行成功，但没有返回结果。"
            return formatted_result

        # Format results
        if len(result_df) > 20:
            formatted_result += f"📊 **查询结果:** 共 {len(result_df)} 行数据，显示前 20 行:\n\n"
            formatted_result += result_df.head(20).to_string(index=False)
            formatted_result += f"\n\n... 还有 {len(result_df) - 20} 行数据。"
        else:
            formatted_result += f"📊 **查询结果:** 共 {len(result_df)} 行数据:\n\n"
            formatted_result += result_df.to_string(index=False)

        # Generate chart if requested and data is suitable
        if generate_chart and len(result_df) > 0 and len(result_df.columns) >= 1:
            try:
                chart_result = chart_generator.generate_chart_from_query_result(formatted_result, query)
                if chart_result.get("success"):
                    formatted_result += f"\n\n📊 **图表已生成**: {chart_result['chart_type']} ({chart_result['analysis']['reason']})"
                    # Store chart data in a global variable for the web service to access
                    global last_generated_chart
                    last_generated_chart = chart_result
                else:
                    formatted_result += f"\n\n⚠️ 图表生成失败: {chart_result.get('error', '未知错误')}"
            except Exception as chart_error:
                logger.error(f"Chart generation error: {chart_error}")
                formatted_result += f"\n\n⚠️ 图表生成出错: {str(chart_error)}"

        return formatted_result

    except Exception as e:
        error_msg = f"Query execution failed: {str(e)}"
        logger.error(error_msg)
        return error_msg

def get_database_schema() -> str:
    """Get database schema information with detailed field descriptions."""
    try:
        tables = clickhouse_client.get_tables()
        schema_info = "Database Schema:\n\n"

        for table in tables:
            schema_info += f"Table: {table}\n"
            schema = clickhouse_client.get_table_schema(table)
            for column in schema:
                # Build field description with type and comment
                field_desc = f"  - {column['name']}: {column['type']}"

                # Add comment if available
                if column.get('comment') and column['comment'].strip():
                    field_desc += f" -- {column['comment']}"
                else:
                    # Infer meaning from field name if no comment
                    inferred_meaning = infer_field_meaning(column['name'])
                    if inferred_meaning:
                        field_desc += f" -- {inferred_meaning}"

                schema_info += field_desc + "\n"
            schema_info += "\n"

        return schema_info
    except Exception as e:
        return f"Error retrieving schema: {str(e)}"

def infer_field_meaning(field_name: str) -> str:
    """Infer field meaning from field name."""
    field_lower = field_name.lower()

    # Common field patterns and their meanings
    field_patterns = {
        'id': '唯一标识符',
        'name': '名称',
        'email': '邮箱地址',
        'phone': '电话号码',
        'address': '地址',
        'date': '日期',
        'time': '时间',
        'created': '创建时间',
        'updated': '更新时间',
        'modified': '修改时间',
        'price': '价格',
        'amount': '金额',
        'total': '总计',
        'count': '数量',
        'quantity': '数量',
        'region': '地区',
        'country': '国家',
        'city': '城市',
        'category': '分类',
        'type': '类型',
        'status': '状态',
        'age': '年龄',
        'gender': '性别',
        'product': '产品',
        'customer': '客户',
        'user': '用户',
        'order': '订单',
        'sale': '销售',
        'revenue': '收入',
        'profit': '利润',
        'cost': '成本',
        'unit': '单位',
        'description': '描述',
        'comment': '备注',
        'note': '注释',
        'registration': '注册',
        'login': '登录'
    }

    # Check for exact matches first
    if field_lower in field_patterns:
        return field_patterns[field_lower]

    # Check for partial matches
    for pattern, meaning in field_patterns.items():
        if pattern in field_lower:
            return meaning

    # Special cases for compound field names
    if 'total_amount' in field_lower:
        return '总金额'
    elif 'unit_price' in field_lower:
        return '单价'
    elif 'sale_date' in field_lower:
        return '销售日期'
    elif 'customer_id' in field_lower:
        return '客户ID'
    elif 'product_id' in field_lower:
        return '产品ID'
    elif 'created_date' in field_lower:
        return '创建日期'
    elif 'registration_date' in field_lower:
        return '注册日期'

    return ""  # Return empty string if no pattern matches

def get_table_sample(table_name: str, limit: int = 5) -> str:
    """Get sample data from a table."""
    try:
        sample_df = clickhouse_client.get_table_sample(table_name, limit)
        return f"Sample data from {table_name}:\n\n{sample_df.to_string(index=False)}"
    except Exception as e:
        return f"Error retrieving sample data: {str(e)}"

def execute_clickhouse_query_with_chart(query: str) -> str:
    """Execute a ClickHouse query and generate chart if suitable."""
    return execute_clickhouse_query(query, generate_chart=True)

def get_conversation_history(session_id: str) -> List[Dict[str, str]]:
    """Get conversation history for a session."""
    return conversation_histories.get(session_id, [])

def add_to_conversation_history(session_id: str, role: str, content: str):
    """Add a message to conversation history."""
    if session_id not in conversation_histories:
        conversation_histories[session_id] = []

    conversation_histories[session_id].append({
        "role": role,
        "content": content,
        "timestamp": datetime.now().isoformat()
    })

    # Keep only the last MAX_HISTORY_LENGTH messages
    if len(conversation_histories[session_id]) > MAX_HISTORY_LENGTH:
        conversation_histories[session_id] = conversation_histories[session_id][-MAX_HISTORY_LENGTH:]

def format_conversation_context(session_id: str) -> str:
    """Format conversation history as context for the AI agent."""
    history = get_conversation_history(session_id)
    if not history:
        return ""

    context = "\n\nPrevious conversation context:\n"
    for msg in history[-6:]:  # Show last 6 messages for context
        role_label = "User" if msg["role"] == "user" else "Assistant"
        context += f"{role_label}: {msg['content'][:200]}...\n"

    context += "\nUse this context to provide relevant follow-up responses and reference previous queries when appropriate.\n"
    return context

async def optimize_prompt(original_question: str, database_info: Dict[str, Any]) -> str:
    """Optimize user's question for better database analysis."""
    model_client = get_model_client()

    # Create a simple agent for prompt optimization
    optimizer_agent = AssistantAgent(
        name="PromptOptimizer",
        model_client=model_client,
        system_message=f"""
        You are a prompt optimization specialist for database queries. Your task is to improve user questions to get better, more comprehensive analysis results.

        Available database tables:
        {', '.join(database_info.get('tables', []))}

        Guidelines for optimization:
        1. Make vague questions more specific
        2. Suggest relevant metrics and dimensions
        3. Add context for better analysis
        4. Ensure questions are clear and actionable
        5. Keep the user's original intent

        Return ONLY the optimized question, nothing else.
        """
    )

    optimization_prompt = f"""
    Original question: "{original_question}"

    Please optimize this question to get better database analysis results. Make it more specific and comprehensive while keeping the original intent.
    """

    try:
        response = await optimizer_agent.run(task=optimization_prompt)
        optimized_question = response.messages[-1].content.strip()

        # Remove any quotes or extra formatting
        if optimized_question.startswith('"') and optimized_question.endswith('"'):
            optimized_question = optimized_question[1:-1]

        return optimized_question
    except Exception as e:
        logger.error(f"Prompt optimization failed: {e}")
        return original_question  # Return original if optimization fails

async def decompose_complex_task(question: str, database_info: Dict[str, Any]) -> List[str]:
    """Decompose complex questions into smaller, manageable sub-tasks."""
    model_client = get_model_client()

    # Create a task decomposition agent
    decomposer_agent = AssistantAgent(
        name="TaskDecomposer",
        model_client=model_client,
        system_message=f"""
        You are a task decomposition specialist for database analysis. Break down complex questions into smaller, logical sub-tasks.

        Available database tables:
        {', '.join(database_info.get('tables', []))}

        Guidelines:
        1. Break complex questions into 2-5 logical sub-tasks
        2. Each sub-task should be specific and actionable
        3. Sub-tasks should build upon each other logically
        4. Focus on data analysis steps
        5. Each sub-task should be a clear question or instruction

        Return the sub-tasks as a numbered list, one per line.
        Example format:
        1. First sub-task question
        2. Second sub-task question
        3. Third sub-task question
        """
    )

    decomposition_prompt = f"""
    Complex question: "{question}"

    Please break this down into smaller, logical sub-tasks for database analysis. Each sub-task should be a specific question or instruction.
    """

    try:
        response = await decomposer_agent.run(task=decomposition_prompt)
        decomposition_text = response.messages[-1].content.strip()

        # Parse the numbered list into individual tasks
        tasks = []
        for line in decomposition_text.split('\n'):
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('-') or line.startswith('•')):
                # Remove numbering and clean up
                task = line.split('.', 1)[-1].strip() if '.' in line else line[1:].strip()
                if task:
                    tasks.append(task)

        # If parsing failed, return the original question
        if not tasks:
            return [question]

        return tasks
    except Exception as e:
        logger.error(f"Task decomposition failed: {e}")
        return [question]  # Return original question if decomposition fails

# FastAPI app
app = FastAPI(
    title="AutoGen + ClickHouse Web Service",
    description="AI-powered database query interface",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class QueryRequest(BaseModel):
    question: str
    include_raw_data: bool = True
    session_id: Optional[str] = None  # For conversation context
    optimize_prompt: bool = False  # Enable prompt optimization
    enable_task_decomposition: bool = False  # Enable complex task decomposition
    generate_charts: bool = False  # Enable automatic chart generation

class SQLExecuteRequest(BaseModel):
    sql: str
    export_format: Optional[str] = None  # 'csv', 'json', or None

class QueryResponse(BaseModel):
    answer: str
    raw_data: Optional[Dict[str, Any]] = None
    execution_time: float
    timestamp: str
    executed_queries: Optional[List[str]] = None  # Store executed SQL queries

class DatabaseInfo(BaseModel):
    tables: List[str]
    total_records: Dict[str, int]
    table_schemas: Dict[str, List[Dict[str, str]]]

# Old ConnectionManager removed - using the new one defined above

def get_model_client():
    """Get the model client with proper configuration."""
    model_name = LLM_CONFIG["model"]
    
    # Check if it's a standard OpenAI model
    standard_openai_models = [
        "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]
    
    if model_name in standard_openai_models:
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url")
        )
    else:
        model_info = ModelInfo(
            family="openai",
            vision=False,
            function_calling=True,
            json_output=True,
            structured_output=False
        )
        
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            model_info=model_info
        )

def get_database_info() -> Dict[str, Any]:
    """Get comprehensive database information."""
    try:
        tables = clickhouse_client.get_tables()
        
        # Get table info
        table_info = {}
        schema_info = {}
        
        for table in tables:
            # Get record count
            count_result = clickhouse_client.execute_query(f"SELECT COUNT(*) as count FROM {table}")
            table_info[table] = int(count_result.iloc[0]['count'])
            
            # Get schema
            schema = clickhouse_client.get_table_schema(table)
            schema_info[table] = schema
        
        return {
            "tables": tables,
            "total_records": table_info,
            "table_schemas": schema_info
        }
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        return {"error": str(e)}

def execute_safe_query(query: str) -> Dict[str, Any]:
    """Execute a query safely and return formatted results."""
    try:
        # Basic security check
        query_lower = query.lower().strip()
        dangerous_keywords = ['drop', 'delete', 'insert', 'update', 'create', 'alter', 'truncate']
        
        if any(keyword in query_lower for keyword in dangerous_keywords):
            return {"error": "Only SELECT queries are allowed for security reasons."}
        
        if not query_lower.startswith('select'):
            return {"error": "Only SELECT queries are allowed."}
        
        # Execute query
        result_df = clickhouse_client.execute_query(query)
        
        if result_df.empty:
            return {"data": [], "columns": [], "row_count": 0}
        
        # Convert to JSON-serializable format
        return {
            "data": result_df.to_dict('records'),
            "columns": list(result_df.columns),
            "row_count": len(result_df),
            "preview": result_df.head(10).to_dict('records') if len(result_df) > 10 else result_df.to_dict('records')
        }
        
    except Exception as e:
        logger.error(f"Query execution error: {e}")
        return {"error": str(e)}

async def create_ai_analyst(database_info: Dict[str, Any], session_id: str = None, enable_charts: bool = False) -> AssistantAgent:
    """Create an AI analyst with current database information and SQL execution tools."""
    model_client = get_model_client()

    # Format database information for the system message
    db_summary = "Database Information:\n\n"

    for table, count in database_info.get("total_records", {}).items():
        db_summary += f"Table: {table} ({count:,} records)\n"

        # Add detailed schema info with field meanings
        schema = database_info.get("table_schemas", {}).get(table, [])
        for column in schema:
            field_desc = f"  - {column['name']}: {column['type']}"

            # Add comment if available
            if column.get('comment') and column['comment'].strip():
                field_desc += f" -- {column['comment']}"
            else:
                # Infer meaning from field name if no comment
                inferred_meaning = infer_field_meaning(column['name'])
                if inferred_meaning:
                    field_desc += f" -- {inferred_meaning}"

            db_summary += field_desc + "\n"
        db_summary += "\n"

    # Add conversation context if available
    conversation_context = ""
    if session_id:
        conversation_context = format_conversation_context(session_id)

    system_message = f"""
    You are an AI Data Analyst with access to a ClickHouse database. Your role is to:

    1. Answer business questions using the available data
    2. Execute SQL queries to retrieve actual data
    3. Provide insights and recommendations based on data patterns
    4. Explain complex data relationships in simple terms

    {db_summary}

    Available tools:
    - execute_clickhouse_query: Execute SELECT queries to get actual data
    - get_database_schema: Get detailed schema information
    - get_table_sample: Get sample data from specific tables
    - execute_clickhouse_query_with_chart: Execute queries and automatically generate charts

    When users ask questions:
    1. First understand what they want to know
    2. Break down complex questions into multiple data requirements
    3. Execute ALL necessary SQL queries to get comprehensive data
    4. For questions requiring multiple data sets (e.g., "sales by region AND product categories"), execute separate queries for each data set
    5. Analyze and explain ALL results with specific data points
    6. Provide actionable insights and recommendations based on all the data

    CRITICAL INSTRUCTIONS FOR SQL GENERATION:
    - ALWAYS use the EXACT field names shown in the database schema above
    - Pay careful attention to field descriptions and meanings provided
    - Use the field comments/meanings to understand what each field represents
    - For comprehensive questions, execute MULTIPLE queries to provide complete answers
    - Don't stop after one query - continue executing additional queries until you have all the data needed
    - When asked for "X AND Y", execute separate queries for X and for Y
    - Always execute queries to provide real data in your responses
    - Use only SELECT queries for security
    - ALWAYS show the SQL query you're executing before showing results

    FIELD MAPPING REMINDERS:
    - sales table has 'region' field (地区)
    - customers table has 'country' field (国家)
    - Use 'total_amount' for sales totals (总金额)
    - Use 'unit_price' for product prices (单价)
    - Use 'sale_date' for sales dates (销售日期)

    EXAMPLE: If asked "Show me sales by region AND product categories":
    1. Execute: SELECT region, SUM(total_amount) FROM sales GROUP BY region ORDER BY SUM(total_amount) DESC
    2. Execute: SELECT p.category, SUM(s.total_amount) FROM sales s JOIN products p ON s.product_id = p.id GROUP BY p.category ORDER BY SUM(s.total_amount) DESC
    3. Analyze both results and provide comprehensive insights

    {conversation_context}
    """

    # Define tools for the agent
    tools = [
        execute_clickhouse_query,
        get_database_schema,
        get_table_sample
    ]

    # Add chart generation tool if enabled
    if enable_charts:
        tools.append(execute_clickhouse_query_with_chart)
        # Update system message to mention chart capabilities
        conversation_context += "\n\n🎨 **Chart Generation Enabled**: Use execute_clickhouse_query_with_chart when users want visual representations of data."

    return AssistantAgent(
        name="DataAnalyst",
        model_client=model_client,
        system_message=system_message,
        tools=tools
    )

# API Routes

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def get_homepage():
    """Serve the main web interface."""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI Data Analysis</title>

        <!-- Modern Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">

        <!-- Material Design Icons -->
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

        <!-- Lucide Icons (更现代的图标) -->
        <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

        <!-- Tailwind CSS with Material Design customization -->
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        fontFamily: {
                            'sans': ['Inter', 'system-ui', 'sans-serif'],
                            'mono': ['JetBrains Mono', 'Menlo', 'Monaco', 'monospace'],
                        },
                        colors: {
                            primary: {
                                50: '#e3f2fd',
                                100: '#bbdefb',
                                200: '#90caf9',
                                300: '#64b5f6',
                                400: '#42a5f5',
                                500: '#2196f3',
                                600: '#1e88e5',
                                700: '#1976d2',
                                800: '#1565c0',
                                900: '#0d47a1',
                            },
                            secondary: {
                                50: '#f3e5f5',
                                100: '#e1bee7',
                                200: '#ce93d8',
                                300: '#ba68c8',
                                400: '#ab47bc',
                                500: '#9c27b0',
                                600: '#8e24aa',
                                700: '#7b1fa2',
                                800: '#6a1b9a',
                                900: '#4a148c',
                            },
                            success: {
                                50: '#e8f5e8',
                                100: '#c8e6c9',
                                200: '#a5d6a7',
                                300: '#81c784',
                                400: '#66bb6a',
                                500: '#4caf50',
                                600: '#43a047',
                                700: '#388e3c',
                                800: '#2e7d32',
                                900: '#1b5e20',
                            },
                            warning: {
                                50: '#fff3e0',
                                100: '#ffe0b2',
                                200: '#ffcc80',
                                300: '#ffb74d',
                                400: '#ffa726',
                                500: '#ff9800',
                                600: '#fb8c00',
                                700: '#f57c00',
                                800: '#ef6c00',
                                900: '#e65100',
                            },
                            error: {
                                50: '#ffebee',
                                100: '#ffcdd2',
                                200: '#ef9a9a',
                                300: '#e57373',
                                400: '#ef5350',
                                500: '#f44336',
                                600: '#e53935',
                                700: '#d32f2f',
                                800: '#c62828',
                                900: '#b71c1c',
                            }
                        },
                        boxShadow: {
                            'material-1': '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
                            'material-2': '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
                            'material-3': '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)',
                            'material-4': '0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)',
                            'material-5': '0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22)',
                        }
                    }
                }
            }
        </script>

        <link rel="stylesheet" href="/static/css/style.css">
    </head>
    <body class="bg-gray-50 min-h-screen font-sans">
        <div id="app">
            <!-- Content will be loaded by JavaScript -->
        </div>
        <script src="/static/js/utils.js"></script>
        <script src="/static/js/utils-new.js"></script>
        <script src="/static/js/task-manager.js"></script>
        <script src="/static/js/task-executors.js"></script>
        <script src="/static/js/app.js"></script>
    </body>
    </html>
    """)

@app.get("/api/database-info", response_model=DatabaseInfo)
async def get_database_info_api():
    """Get database information."""
    try:
        info = get_database_info()
        if "error" in info:
            raise HTTPException(status_code=500, detail=info["error"])
        
        return DatabaseInfo(**info)
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/execute-sql")
async def execute_sql_direct(request: SQLExecuteRequest):
    """Execute SQL query directly with optional export functionality."""
    try:
        # Validate SQL query (basic security check)
        sql_lower = request.sql.lower().strip()
        dangerous_keywords = ['drop', 'delete', 'insert', 'update', 'create', 'alter', 'truncate', 'grant', 'revoke']

        if any(keyword in sql_lower for keyword in dangerous_keywords):
            raise HTTPException(status_code=400, detail="Only SELECT queries are allowed for security reasons.")

        if not sql_lower.startswith('select'):
            raise HTTPException(status_code=400, detail="Only SELECT queries are allowed.")

        # Execute query
        start_time = datetime.now()
        result_df = clickhouse_client.execute_query(request.sql)
        execution_time = (datetime.now() - start_time).total_seconds()

        if result_df.empty:
            return {
                "success": True,
                "message": "Query executed successfully but returned no results.",
                "sql": request.sql,
                "execution_time": execution_time,
                "row_count": 0,
                "data": [],
                "columns": []
            }

        # Prepare response data
        response_data = {
            "success": True,
            "message": f"Query executed successfully. Returned {len(result_df)} rows.",
            "sql": request.sql,
            "execution_time": execution_time,
            "row_count": len(result_df),
            "columns": list(result_df.columns),
            "data": result_df.to_dict('records')
        }

        # Handle export formats
        if request.export_format == 'csv':
            from io import StringIO
            csv_buffer = StringIO()
            result_df.to_csv(csv_buffer, index=False)
            response_data["export_data"] = csv_buffer.getvalue()
            response_data["export_filename"] = f"query_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        elif request.export_format == 'json':
            import json
            response_data["export_data"] = json.dumps(result_df.to_dict('records'), ensure_ascii=False, indent=2)
            response_data["export_filename"] = f"query_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        return response_data

    except Exception as e:
        logger.error(f"SQL execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/query", response_model=QueryResponse)
async def query_data(request: QueryRequest):
    """Process a natural language query with optional optimization and task decomposition."""
    start_time = datetime.now()

    try:
        # Generate session ID if not provided
        session_id = request.session_id or f"session_{datetime.now().timestamp()}"

        # Clear executed queries for this session
        global executed_queries
        executed_queries = []

        # Get database info
        db_info = get_database_info()
        if "error" in db_info:
            raise HTTPException(status_code=500, detail=db_info["error"])

        # Store original question
        original_question = request.question
        processed_question = original_question

        # Step 1: Optimize prompt if requested
        if request.optimize_prompt:
            logger.info("Optimizing prompt...")
            processed_question = await optimize_prompt(original_question, db_info)
            logger.info(f"Optimized question: {processed_question}")

        # Step 2: Decompose task if requested
        sub_tasks = []
        if request.enable_task_decomposition:
            logger.info("Decomposing complex task...")
            sub_tasks = await decompose_complex_task(processed_question, db_info)
            logger.info(f"Decomposed into {len(sub_tasks)} sub-tasks")

        # Add user question to conversation history
        add_to_conversation_history(session_id, "user", original_question)

        # Create AI analyst with conversation context
        analyst = await create_ai_analyst(db_info, session_id, request.generate_charts)

        # Process the question(s)
        if sub_tasks and len(sub_tasks) > 1:
            # Execute sub-tasks sequentially with fresh agent instances
            all_responses = []
            for i, sub_task in enumerate(sub_tasks, 1):
                logger.info(f"Executing sub-task {i}/{len(sub_tasks)}: {sub_task}")

                # Create a fresh agent for each sub-task to avoid message history issues
                sub_analyst = await create_ai_analyst(db_info, session_id, request.generate_charts)
                sub_response = await sub_analyst.run(task=f"Sub-task {i}: {sub_task}")
                sub_answer = sub_response.messages[-1].content
                all_responses.append(f"**Sub-task {i}**: {sub_task}\n\n{sub_answer}")

            # Combine all responses
            answer = "\n\n" + "="*50 + "\n\n".join(all_responses)
            answer = f"**Complex Task Analysis** (decomposed into {len(sub_tasks)} sub-tasks):\n\n" + answer
        else:
            # Single task execution
            response = await analyst.run(task=processed_question)
            answer = response.messages[-1].content

            # Add optimization note if prompt was optimized
            if request.optimize_prompt and processed_question != original_question:
                answer = f"**Optimized Question**: {processed_question}\n\n**Analysis**:\n{answer}"

        # Add assistant response to conversation history
        add_to_conversation_history(session_id, "assistant", answer)

        # Handle chart generation if requested
        chart_data = None
        if request.generate_charts and last_generated_chart:
            chart_data = last_generated_chart
            # Store chart in session
            if session_id not in session_charts:
                session_charts[session_id] = []
            session_charts[session_id].append(chart_data)

        # Get raw data if requested
        raw_data = None
        if request.include_raw_data:
            raw_data = {
                "database_info": db_info,
                "original_question": original_question,
                "processed_question": processed_question,
                "sub_tasks": sub_tasks if sub_tasks else None,
                "session_id": session_id,
                "conversation_history": get_conversation_history(session_id),
                "optimization_enabled": request.optimize_prompt,
                "decomposition_enabled": request.enable_task_decomposition,
                "chart_generation_enabled": request.generate_charts,
                "chart_data": chart_data
            }

        execution_time = (datetime.now() - start_time).total_seconds()

        return QueryResponse(
            answer=answer,
            raw_data=raw_data,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat(),
            executed_queries=executed_queries.copy()  # Include executed queries
        )

    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time task updates."""
    logger.info(f"WebSocket connection attempt from client: {client_id}")

    try:
        await manager.connect(websocket, client_id)
        logger.info(f"WebSocket connected successfully: {client_id}")

        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            logger.info(f"WebSocket message from {client_id}: {data}")

            try:
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "ping":
                    await manager.send_personal_message({"type": "pong"}, client_id)
                elif message.get("type") == "get_tasks":
                    # Send current tasks to client
                    tasks_data = [task.to_dict() for task in tasks_storage.values()]
                    await manager.send_personal_message({
                        "type": "tasks_list",
                        "data": tasks_data
                    }, client_id)
                    logger.info(f"Sent {len(tasks_data)} tasks to client {client_id}")

            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON from client {client_id}: {e}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, client_id)

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {client_id}")
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        manager.disconnect(client_id)

@app.get("/api/test-websocket")
async def test_websocket():
    """Test endpoint to verify server is working."""
    return {
        "message": "Server is working",
        "websocket_endpoint": "/ws/{client_id}",
        "active_connections": len(manager.active_connections),
        "tasks_count": len(tasks_storage)
    }

# New Async Task API Endpoints

@app.post("/api/tasks/ai-query")
async def create_ai_query_task(request: QueryRequest, background_tasks: BackgroundTasks):
    """Create an async AI query task with step decomposition."""
    task_id = str(uuid.uuid4())

    task = AsyncTask(
        task_id=task_id,
        name=f"AI Query: {request.question[:50]}...",
        task_type="ai_query",
        metadata={
            "question": request.question,
            "options": {
                "include_raw_data": request.include_raw_data,
                "session_id": request.session_id,
                "optimize_prompt": getattr(request, 'optimize_prompt', False),
                "enable_task_decomposition": getattr(request, 'enable_task_decomposition', False),
                "generate_charts": getattr(request, 'generate_charts', False)
            }
        }
    )

    tasks_storage[task_id] = task

    # Execute task in background
    background_tasks.add_task(TaskExecutor.execute_ai_query, task)

    return {"task_id": task_id, "status": "created", "message": "AI query task created"}

@app.post("/api/tasks/sql-execute")
async def create_sql_execute_task(request: SQLExecuteRequest, background_tasks: BackgroundTasks):
    """Create an async SQL execution task with step decomposition."""
    task_id = str(uuid.uuid4())

    task = AsyncTask(
        task_id=task_id,
        name=f"SQL: {request.sql[:50]}...",
        task_type="sql_execute",
        metadata={
            "sql": request.sql,
            "export_format": request.export_format
        }
    )

    tasks_storage[task_id] = task

    # Execute task in background
    background_tasks.add_task(TaskExecutor.execute_sql_query_task, task)

    return {"task_id": task_id, "status": "created", "message": "SQL execution task created"}

@app.get("/api/tasks/{task_id}")
async def get_task_status(task_id: str):
    """Get the status of a specific task."""
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_storage[task_id]
    return task.to_dict()

@app.get("/api/tasks")
async def get_all_tasks():
    """Get all tasks."""
    return [task.to_dict() for task in tasks_storage.values()]

@app.delete("/api/tasks/{task_id}")
async def cancel_task(task_id: str):
    """Cancel a specific task."""
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task = tasks_storage[task_id]
    if task.status in [TaskStatus.PENDING, TaskStatus.PLANNING, TaskStatus.EXECUTING]:
        task.status = TaskStatus.CANCELLED
        task.end_time = datetime.now()

        # Update current step
        current_step = task.get_current_step()
        if current_step and current_step.status == TaskStatus.EXECUTING:
            current_step.status = TaskStatus.CANCELLED
            current_step.end_time = datetime.now()

        await manager.send_task_update(task)
        return {"message": "Task cancelled"}
    else:
        return {"message": "Task cannot be cancelled"}

@app.delete("/api/tasks")
async def clear_completed_tasks():
    """Clear all completed tasks."""
    completed_tasks = [
        task_id for task_id, task in tasks_storage.items()
        if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
    ]

    for task_id in completed_tasks:
        del tasks_storage[task_id]

    return {"message": f"Cleared {len(completed_tasks)} completed tasks"}

@app.post("/api/download-data")
async def download_data(request: dict):
    """Download data in specified format based on SQL query."""
    try:
        sql = request.get('sql')
        format_type = request.get('format', 'csv').lower()
        filename = request.get('filename')

        if not sql:
            raise HTTPException(status_code=400, detail="SQL query is required")

        # Execute the SQL query
        result = execute_sql_safely(sql)
        data = result.get('data', [])

        if not data:
            raise HTTPException(status_code=404, detail="No data returned from query")

        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"query_result_{timestamp}.{format_type}"

        # Generate content based on format
        if format_type == 'csv':
            content = generate_csv_content(data)
            media_type = 'text/csv'
        elif format_type == 'json':
            content = json.dumps(data, indent=2, default=str)
            media_type = 'application/json'
        else:
            raise HTTPException(status_code=400, detail="Unsupported format. Use 'csv' or 'json'")

        # Return file response
        return Response(
            content=content,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )

    except Exception as e:
        logger.error(f"Download data error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def generate_csv_content(data: list) -> str:
    """Generate CSV content from data list."""
    if not data:
        return ''

    import csv
    import io

    output = io.StringIO()
    fieldnames = data[0].keys()
    writer = csv.DictWriter(output, fieldnames=fieldnames)
    writer.writeheader()
    writer.writerows(data)

    return output.getvalue()

@app.post("/api/export-query-result")
async def export_query_result(request: SQLExecuteRequest):
    """Export the result of a specific query."""
    try:
        # Validate and execute the query
        sql_lower = request.sql.lower().strip()
        if not sql_lower.startswith('select'):
            raise HTTPException(status_code=400, detail="Only SELECT queries are allowed.")

        # Execute query
        result_df = clickhouse_client.execute_query(request.sql)

        if result_df.empty:
            raise HTTPException(status_code=404, detail="Query returned no results to export.")

        # Generate export data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if request.export_format == 'csv':
            from io import StringIO
            csv_buffer = StringIO()
            result_df.to_csv(csv_buffer, index=False)
            content = csv_buffer.getvalue()
            filename = f"query_result_{timestamp}.csv"
            media_type = "text/csv"
        elif request.export_format == 'json':
            import json
            content = json.dumps(result_df.to_dict('records'), ensure_ascii=False, indent=2)
            filename = f"query_result_{timestamp}.json"
            media_type = "application/json"
        else:
            raise HTTPException(status_code=400, detail="Invalid export format. Use 'csv' or 'json'.")

        # Return file response
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"Export error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        tables = clickhouse_client.get_tables()
        return {
            "status": "healthy",
            "database_connected": True,
            "tables_count": len(tables),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    uvicorn.run(
        "web_service:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
