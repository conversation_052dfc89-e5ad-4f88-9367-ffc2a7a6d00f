#!/usr/bin/env python3
"""
Interactive AutoGen + ClickHouse Demo with actual database execution.
"""

import os
import sys
import asyncio
from typing import Any, Dict, List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check LLM provider and required environment variables
llm_provider = os.getenv('LLM_PROVIDER', 'openai').lower()

if llm_provider == 'openai':
    required_env_vars = ['OPENAI_API_KEY']
else:
    print(f"❌ Unsupported LLM provider: {llm_provider}")
    sys.exit(1)

missing_vars = [var for var in required_env_vars if not os.getenv(var)]

if missing_vars:
    print(f"❌ Missing required environment variables for {llm_provider}: {missing_vars}")
    print("Please set them in your .env file")
    sys.exit(1)

print(f"✅ Using LLM provider: {llm_provider}")

try:
    from clickhouse_client import clickhouse_client
    from autogen_agentchat.agents import AssistantAgent
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_core.models import ModelInfo
    from config import LLM_CONFIG
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install -U 'autogen-agentchat' 'autogen-ext[openai]'")
    sys.exit(1)

def check_database_connection():
    """Check if database is accessible."""
    try:
        tables = clickhouse_client.get_tables()
        if not tables:
            print("⚠️  No tables found. Please run setup_database.py first.")
            return False
        print(f"✅ Connected to database. Found tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Please ensure ClickHouse is running and configured correctly.")
        return False

def get_model_client():
    """Get the model client with proper model_info for non-OpenAI models."""
    model_name = LLM_CONFIG["model"]
    
    # Check if it's a standard OpenAI model
    standard_openai_models = [
        "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]
    
    if model_name in standard_openai_models:
        # Standard OpenAI model - no model_info needed
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url")
        )
    else:
        # Non-standard model - need to provide model_info
        model_info = ModelInfo(
            family="openai",  # Required field
            vision=False,
            function_calling=True,
            json_output=True,
            structured_output=False  # Add this to avoid warnings
        )
        
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            model_info=model_info
        )

# Database tool functions
def execute_clickhouse_query(query: str) -> str:
    """
    Execute a ClickHouse query and return results as formatted string.
    
    Args:
        query: SQL query to execute
        
    Returns:
        Formatted query results or error message
    """
    try:
        # Validate query (basic security check)
        query_lower = query.lower().strip()
        if not query_lower.startswith('select'):
            return "Error: Only SELECT queries are allowed for security reasons."
        
        # Execute query
        result_df = clickhouse_client.execute_query(query)
        
        if result_df.empty:
            return "Query executed successfully but returned no results."
        
        # Format results
        if len(result_df) > 20:
            formatted_result = f"Query returned {len(result_df)} rows. Showing first 20:\n\n"
            formatted_result += result_df.head(20).to_string(index=False)
            formatted_result += f"\n\n... and {len(result_df) - 20} more rows."
        else:
            formatted_result = f"Query returned {len(result_df)} rows:\n\n"
            formatted_result += result_df.to_string(index=False)
        
        return formatted_result
        
    except Exception as e:
        error_msg = f"Query execution failed: {str(e)}"
        return error_msg

def get_database_schema() -> str:
    """Get database schema information."""
    try:
        tables = clickhouse_client.get_tables()
        schema_info = "Database Schema:\n\n"
        
        for table in tables:
            schema_info += f"Table: {table}\n"
            schema = clickhouse_client.get_table_schema(table)
            for column in schema:
                schema_info += f"  - {column['name']}: {column['type']}\n"
            schema_info += "\n"
        
        return schema_info
    except Exception as e:
        return f"Error retrieving schema: {str(e)}"

def get_table_sample(table_name: str, limit: int = 5) -> str:
    """Get sample data from a table."""
    try:
        sample_df = clickhouse_client.get_table_sample(table_name, limit)
        return f"Sample data from {table_name}:\n\n{sample_df.to_string(index=False)}"
    except Exception as e:
        return f"Error retrieving sample data: {str(e)}"

async def create_database_agent():
    """Create an agent that can execute database queries."""
    model_client = get_model_client()

    # Get database info
    schema_info = get_database_schema()

    system_message = f"""
    You are a Database Agent that can execute ClickHouse queries and analyze data.

    {schema_info}

    When users ask questions about data:
    1. First understand what they want to know
    2. Write appropriate SQL queries to get the data
    3. Execute the queries using the available tools
    4. Analyze and explain the results

    Always use SELECT queries only for security. Provide insights and explanations with your results.

    Example queries you can run:
    - SELECT region, SUM(total_amount) FROM sales GROUP BY region
    - SELECT p.category, COUNT(*) FROM products p GROUP BY p.category
    - SELECT c.country, COUNT(*) FROM customers c GROUP BY c.country
    """

    # Define tools for the agent
    tools = [
        execute_clickhouse_query,
        get_database_schema,
        get_table_sample
    ]

    return AssistantAgent(
        name="DatabaseAgent",
        model_client=model_client,
        system_message=system_message,
        tools=tools
    )

async def interactive_database_chat():
    """Interactive chat with database agent."""
    print("\n" + "="*60)
    print("INTERACTIVE DATABASE CHAT")
    print("="*60)
    print("Ask questions about the data and I'll query the database for you!")
    print("Type 'quit' to exit")
    print("-" * 60)
    
    # Create agent
    agent = await create_database_agent()
    
    # Example questions
    print("💡 Example questions you can ask:")
    print("- What are the total sales by region?")
    print("- Which products are selling best?")
    print("- Show me customer demographics by country")
    print("- What's the average order value?")
    print("- Which region has the highest sales?")
    print("-" * 60)
    
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            print("🤖 Analyzing and querying database...")
            
            # Create a task that includes the user question and instructions to use tools
            task = f"""
            User question: {user_input}
            
            Please help answer this question by:
            1. Understanding what data is needed
            2. Writing and executing appropriate SQL queries
            3. Analyzing the results and providing insights
            
            Use the available functions to query the database and provide a comprehensive answer.
            """
            
            response = await agent.run(task=task)
            print(f"\n🤖 Database Agent: {response.messages[-1].content}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

async def run_predefined_analysis():
    """Run some predefined analysis examples."""
    print("\n" + "="*60)
    print("PREDEFINED ANALYSIS EXAMPLES")
    print("="*60)
    
    agent = await create_database_agent()
    
    analyses = [
        "What are the total sales by region? Show me the data and explain which regions are performing best.",
        "Which product categories are most popular? Give me the sales data by category.",
        "Show me the top 5 customers by total purchase amount and their demographics.",
        "What's the monthly sales trend? Analyze sales performance over time."
    ]
    
    for i, analysis in enumerate(analyses, 1):
        print(f"\n📊 Analysis {i}: {analysis}")
        print("-" * 50)
        
        try:
            response = await agent.run(task=analysis)
            print(f"🤖 Result: {response.messages[-1].content}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 50)

async def main():
    """Main function."""
    print("🚀 Interactive AutoGen + ClickHouse Demo")
    print("=" * 60)
    print("This demo shows AI agents actually executing database queries!")
    print("=" * 60)
    
    # Check database connection
    if not check_database_connection():
        return 1
    
    # Show menu
    while True:
        print("\nSelect a demo:")
        print("1. Interactive Database Chat")
        print("2. Predefined Analysis Examples")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            await interactive_database_chat()
        elif choice == "2":
            await run_predefined_analysis()
        elif choice == "3":
            print("Goodbye! 👋")
            break
        else:
            print("❌ Invalid choice. Please try again.")
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\nDemo interrupted. Goodbye! 👋")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Demo error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        try:
            clickhouse_client.close()
        except:
            pass
