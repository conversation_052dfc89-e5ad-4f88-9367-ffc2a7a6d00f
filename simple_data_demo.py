#!/usr/bin/env python3
"""
Simple AutoGen + ClickHouse Demo without tools (compatible with more APIs).
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check LLM provider and required environment variables
llm_provider = os.getenv('LLM_PROVIDER', 'openai').lower()

if llm_provider == 'openai':
    required_env_vars = ['OPENAI_API_KEY']
else:
    print(f"❌ Unsupported LLM provider: {llm_provider}")
    sys.exit(1)

missing_vars = [var for var in required_env_vars if not os.getenv(var)]

if missing_vars:
    print(f"❌ Missing required environment variables for {llm_provider}: {missing_vars}")
    print("Please set them in your .env file")
    sys.exit(1)

print(f"✅ Using LLM provider: {llm_provider}")

try:
    from clickhouse_client import clickhouse_client
    from autogen_agentchat.agents import AssistantAgent
    from autogen_ext.models.openai import OpenAIChatCompletionClient
    from autogen_core.models import ModelInfo
    from config import LLM_CONFIG
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install -U 'autogen-agentchat' 'autogen-ext[openai]'")
    sys.exit(1)

def check_database_connection():
    """Check if database is accessible."""
    try:
        tables = clickhouse_client.get_tables()
        if not tables:
            print("⚠️  No tables found. Please run setup_database.py first.")
            return False
        print(f"✅ Connected to database. Found tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Please ensure ClickHouse is running and configured correctly.")
        return False

def get_model_client():
    """Get the model client with proper model_info for non-OpenAI models."""
    model_name = LLM_CONFIG["model"]
    
    # Check if it's a standard OpenAI model
    standard_openai_models = [
        "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]
    
    if model_name in standard_openai_models:
        # Standard OpenAI model - no model_info needed
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url")
        )
    else:
        # Non-standard model - need to provide model_info
        model_info = ModelInfo(
            family="openai",  # Required field
            vision=False,
            function_calling=True,
            json_output=True,
            structured_output=False  # Add this to avoid warnings
        )
        
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            model_info=model_info
        )

def get_current_data():
    """Get current data from the database."""
    try:
        # Get sales by region
        sales_by_region = clickhouse_client.execute_query(
            "SELECT region, SUM(total_amount) as total_sales FROM sales GROUP BY region ORDER BY total_sales DESC"
        )
        
        # Get top products
        top_products = clickhouse_client.execute_query(
            """
            SELECT p.name, p.category, SUM(s.total_amount) as total_sales 
            FROM sales s 
            JOIN products p ON s.product_id = p.id 
            GROUP BY p.name, p.category 
            ORDER BY total_sales DESC 
            LIMIT 10
            """
        )
        
        # Get customer demographics
        customer_demographics = clickhouse_client.execute_query(
            "SELECT country, COUNT(*) as customer_count, AVG(age) as avg_age FROM customers GROUP BY country ORDER BY customer_count DESC"
        )
        
        # Get category performance
        category_performance = clickhouse_client.execute_query(
            """
            SELECT p.category, COUNT(s.id) as sales_count, SUM(s.total_amount) as total_sales 
            FROM sales s 
            JOIN products p ON s.product_id = p.id 
            GROUP BY p.category 
            ORDER BY total_sales DESC
            """
        )
        
        return {
            "sales_by_region": sales_by_region.to_string(index=False),
            "top_products": top_products.to_string(index=False),
            "customer_demographics": customer_demographics.to_string(index=False),
            "category_performance": category_performance.to_string(index=False)
        }
    except Exception as e:
        return {"error": str(e)}

async def create_data_analyst():
    """Create a data analyst agent with current data."""
    model_client = get_model_client()
    
    # Get current data
    data = get_current_data()
    
    if "error" in data:
        print(f"❌ Error getting data: {data['error']}")
        return None
    
    system_message = f"""
    You are a Business Data Analyst with access to current sales data from our ClickHouse database.
    
    Current Data Available:
    
    SALES BY REGION:
    {data['sales_by_region']}
    
    TOP 10 PRODUCTS BY SALES:
    {data['top_products']}
    
    CUSTOMER DEMOGRAPHICS BY COUNTRY:
    {data['customer_demographics']}
    
    CATEGORY PERFORMANCE:
    {data['category_performance']}
    
    When users ask questions about the business data, analyze the information above and provide:
    1. Direct answers based on the data
    2. Business insights and trends
    3. Actionable recommendations
    4. Comparisons and patterns you notice
    
    Always reference specific numbers from the data when making points.
    Be analytical and provide business value in your responses.
    """
    
    return AssistantAgent(
        name="DataAnalyst",
        model_client=model_client,
        system_message=system_message
    )

async def interactive_analysis():
    """Interactive analysis session."""
    print("\n" + "="*60)
    print("INTERACTIVE DATA ANALYSIS")
    print("="*60)
    print("Ask questions about our business data and get AI-powered insights!")
    print("Type 'quit' to exit")
    print("-" * 60)
    
    # Create analyst
    analyst = await create_data_analyst()
    if not analyst:
        return
    
    # Example questions
    print("💡 Example questions you can ask:")
    print("- Which region is performing best and why?")
    print("- What are the top product categories by sales?")
    print("- Which countries have the most customers?")
    print("- What insights can you provide about our sales performance?")
    print("- Which products should we focus on?")
    print("-" * 60)
    
    while True:
        try:
            user_input = input("\n💬 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            print("🤖 Analyzing data...")
            
            response = await analyst.run(task=user_input)
            print(f"\n📊 Data Analyst: {response.messages[-1].content}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

async def run_predefined_analysis():
    """Run predefined analysis questions."""
    print("\n" + "="*60)
    print("PREDEFINED ANALYSIS EXAMPLES")
    print("="*60)
    
    analyst = await create_data_analyst()
    if not analyst:
        return
    
    questions = [
        "What are the key insights about our regional sales performance?",
        "Which product categories are driving the most revenue and what does this tell us?",
        "Analyze our customer base - what geographic patterns do you see?",
        "Based on the data, what are your top 3 business recommendations?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n📊 Analysis {i}: {question}")
        print("-" * 50)
        
        try:
            response = await analyst.run(task=question)
            print(f"🤖 Insight: {response.messages[-1].content}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 50)

async def main():
    """Main function."""
    print("🚀 AutoGen + ClickHouse Data Analysis Demo")
    print("=" * 60)
    print("This demo shows AI-powered analysis of ClickHouse data!")
    print("=" * 60)
    
    # Check database connection
    if not check_database_connection():
        return 1
    
    # Show menu
    while True:
        print("\nSelect a demo:")
        print("1. Interactive Data Analysis")
        print("2. Predefined Analysis Examples")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            await interactive_analysis()
        elif choice == "2":
            await run_predefined_analysis()
        elif choice == "3":
            print("Goodbye! 👋")
            break
        else:
            print("❌ Invalid choice. Please try again.")
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\nDemo interrupted. Goodbye! 👋")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Demo error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        try:
            clickhouse_client.close()
        except:
            pass
