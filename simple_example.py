#!/usr/bin/env python3
"""
Simple example demonstrating AutoGen + ClickHouse integration.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check LLM provider and required environment variables
llm_provider = os.getenv('LLM_PROVIDER', 'ollama').lower()

if llm_provider == 'openai':
    required_env_vars = ['OPENAI_API_KEY']
elif llm_provider == 'azure':
    required_env_vars = ['AZURE_OPENAI_API_KEY', 'AZURE_OPENAI_ENDPOINT']
elif llm_provider == 'ollama':
    required_env_vars = []  # Ollama doesn't require API keys
else:
    print(f"❌ Unsupported LLM provider: {llm_provider}")
    sys.exit(1)

missing_vars = [var for var in required_env_vars if not os.getenv(var)]

if missing_vars:
    print(f"❌ Missing required environment variables for {llm_provider}: {missing_vars}")
    print("Please set them in your .env file")
    sys.exit(1)

print(f"✅ Using LLM provider: {llm_provider}")

try:
    from clickhouse_client import clickhouse_client
    from agents import AgentFactory, execute_clickhouse_query
    import autogen
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please run: pip install -r requirements.txt")
    sys.exit(1)

def check_database_connection():
    """Check if database is accessible."""
    try:
        tables = clickhouse_client.get_tables()
        if not tables:
            print("⚠️  No tables found. Please run setup_database.py first.")
            return False
        print(f"✅ Connected to database. Found tables: {tables}")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Please ensure ClickHouse is running and configured correctly.")
        return False

def simple_query_example():
    """Demonstrate simple database queries."""
    print("\n" + "="*50)
    print("SIMPLE QUERY EXAMPLE")
    print("="*50)
    
    # Example queries
    queries = [
        ("Total number of sales", "SELECT COUNT(*) as total_sales FROM sales"),
        ("Sales by region", "SELECT region, COUNT(*) as sales_count FROM sales GROUP BY region"),
        ("Top 3 products", """
            SELECT p.name, SUM(s.total_amount) as total_sales 
            FROM sales s 
            JOIN products p ON s.product_id = p.id 
            GROUP BY p.name 
            ORDER BY total_sales DESC 
            LIMIT 3
        """)
    ]
    
    for description, query in queries:
        print(f"\n{description}:")
        print(f"Query: {query.strip()}")
        result = execute_clickhouse_query(query)
        print(f"Result:\n{result}")
        print("-" * 40)

def simple_agent_example():
    """Demonstrate simple agent interaction."""
    print("\n" + "="*50)
    print("SIMPLE AGENT EXAMPLE")
    print("="*50)
    
    try:
        # Create a simple data agent
        from config import AGENT_CONFIG
        from agents import get_llm_config_for_autogen

        llm_config = get_llm_config_for_autogen()
        
        # Create a data agent
        data_agent = autogen.AssistantAgent(
            name="DataAgent",
            system_message="""
            You are a helpful data analyst. You can execute ClickHouse queries to analyze data.
            When asked about data, write and execute appropriate SQL queries.
            Available functions: execute_clickhouse_query, get_database_schema, get_table_sample
            """,
            llm_config=llm_config,
            max_consecutive_auto_reply=3,
        )
        
        # Create user proxy for automated demo
        user_proxy = autogen.UserProxyAgent(
            name="User",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=1,
            is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
            code_execution_config=False,
        )
        
        # Register functions
        autogen.register_function(
            execute_clickhouse_query,
            caller=data_agent,
            executor=user_proxy,
            name="execute_clickhouse_query",
            description="Execute a SELECT query on ClickHouse database."
        )
        
        print("🤖 Starting agent conversation...")
        
        # Start conversation
        user_proxy.initiate_chat(
            data_agent,
            message="Show me the total sales amount by region. TERMINATE when done."
        )
        
    except Exception as e:
        print(f"❌ Agent example failed: {e}")
        print("This might be due to OpenAI API configuration issues.")

def interactive_example():
    """Simple interactive example."""
    print("\n" + "="*50)
    print("INTERACTIVE EXAMPLE")
    print("="*50)
    print("Enter SQL queries to execute (type 'quit' to exit):")
    
    while True:
        try:
            query = input("\nSQL> ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            if not query.lower().startswith('select'):
                print("❌ Only SELECT queries are allowed for security.")
                continue
            
            result = execute_clickhouse_query(query)
            print(result)
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main function."""
    print("🚀 AutoGen + ClickHouse Simple Example")
    print("="*50)
    
    # Check database connection
    if not check_database_connection():
        return 1
    
    # Show menu
    while True:
        print("\nSelect an example:")
        print("1. Simple Query Example")
        print("2. Simple Agent Example") 
        print("3. Interactive Query Mode")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            simple_query_example()
        elif choice == "2":
            simple_agent_example()
        elif choice == "3":
            interactive_example()
        elif choice == "4":
            print("Goodbye! 👋")
            break
        else:
            print("❌ Invalid choice. Please try again.")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\nExiting...")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        try:
            clickhouse_client.close()
        except:
            pass
