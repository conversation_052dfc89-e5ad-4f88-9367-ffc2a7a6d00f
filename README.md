# AutoGen + ClickHouse Demo

This project demonstrates how to integrate Microsoft AutoGen with ClickHouse database for multi-agent data analysis and querying.

## Features

- **Multi-Agent System**: Uses AutoGen to create specialized agents for different tasks
- **ClickHouse Integration**: Connects to ClickHouse database for data storage and querying
- **Multiple LLM Support**: Works with OpenAI, Ollama (local), and Azure OpenAI
- **Data Analysis**: Agents can perform complex data analysis and generate insights
- **Interactive Chat**: Chat interface for interacting with the agent system

## Architecture

- **Data Agent**: Specialized in querying ClickHouse database
- **Analyst Agent**: Performs data analysis and generates insights
- **User Proxy**: Handles user interactions and coordinates between agents

## Setup

### Prerequisites

- Python 3.8+
- ClickHouse server (local or remote)
- OpenAI API key or compatible LLM endpoint

### Installation

1. Clone this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Initialize ClickHouse database:
   ```bash
   python setup_database.py
   ```

5. Run the demo:
   ```bash
   python main.py
   ```

## Usage

The demo includes sample data and pre-configured agents. You can:

1. Ask questions about the data
2. Request specific queries
3. Get data analysis and insights
4. Generate reports

## Example Queries

- "What are the top 10 products by sales?"
- "Show me the sales trend for the last 6 months"
- "Analyze customer behavior patterns"
- "Generate a summary report of Q4 performance"

## Quick Start

### Option 1: Using Ollama (Local LLM - Recommended)

```bash
# Setup with Ollama
make setup-ollama
```

Or manually:
```bash
# Install Ollama first: https://ollama.ai/download
pip install -r requirements.txt
make start-ollama  # Start ClickHouse + Ollama
python ollama_setup.py  # Setup Ollama models
python setup_database.py  # Initialize database
python main.py  # Run demo
```

### Option 2: Using OpenAI

```bash
# Quick setup with OpenAI
python quick_start.py
```

This script will:
1. Check requirements
2. Install dependencies
3. Start ClickHouse (if Docker is available)
4. Set up the database with sample data
5. Run basic tests

## Manual Setup

### 1. Start ClickHouse

**Option A: Using Docker (Recommended)**
```bash
docker-compose up -d
```

**Option B: Local Installation**
- Install ClickHouse server locally
- Update connection settings in `.env`

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env with your settings
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Initialize Database
```bash
python setup_database.py
```

### 5. Run Tests
```bash
python test_demo.py
```

## Usage Examples

### Command Line Interface
```bash
python main.py
```

### Jupyter Notebook
```bash
jupyter notebook demo_notebook.ipynb
```

### Programmatic Usage
```python
from agents import AgentFactory
from clickhouse_client import clickhouse_client

# Create agents
agents = AgentFactory.create_agents()

# Query database
result = clickhouse_client.execute_query("SELECT * FROM sales LIMIT 5")
print(result)
```

## Project Structure

```
bot-agent/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── .env.example             # Environment configuration template
├── config.py                # Configuration settings
├── clickhouse_client.py     # ClickHouse database client
├── agents.py                # AutoGen agents definition
├── setup_database.py        # Database initialization script
├── main.py                  # Main application
├── test_demo.py             # Test suite
├── quick_start.py           # Quick setup script
├── demo_notebook.ipynb      # Jupyter notebook demo
└── docker-compose.yml       # ClickHouse Docker setup
```

## Configuration

### LLM Provider Configuration

The demo supports multiple LLM providers. Configure in your `.env` file:

**Ollama (Local LLM)**
```bash
LLM_PROVIDER=ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2:7b
```

**OpenAI**
```bash
LLM_PROVIDER=openai
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
```

**OpenAI-Compatible APIs**
```bash
# OpenRouter
LLM_PROVIDER=openai
OPENAI_API_KEY=your_openrouter_key
OPENAI_BASE_URL=https://openrouter.ai/api/v1
OPENAI_MODEL=anthropic/claude-3-sonnet

# Together AI
LLM_PROVIDER=openai
OPENAI_API_KEY=your_together_key
OPENAI_BASE_URL=https://api.together.xyz/v1
OPENAI_MODEL=meta-llama/Llama-2-70b-chat-hf

# Groq
LLM_PROVIDER=openai
OPENAI_API_KEY=your_groq_key
OPENAI_BASE_URL=https://api.groq.com/openai/v1
OPENAI_MODEL=llama2-70b-4096

# Local OpenAI-compatible server
LLM_PROVIDER=openai
OPENAI_API_KEY=not_required
OPENAI_BASE_URL=http://localhost:8000/v1
OPENAI_MODEL=your_local_model
```

**Azure OpenAI**
```bash
LLM_PROVIDER=azure
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
```

### Configuration Testing

Test your LLM configuration:
```bash
python test_llm_config.py
```

This will verify:
- API connectivity
- Model availability
- AutoGen integration

### Other Settings

Edit `config.py` to customize:
- ClickHouse connection settings
- Agent configurations
- Temperature and other LLM parameters

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check if ClickHouse is running
   - Verify connection settings in `.env`
   - Ensure firewall allows connections

2. **LLM Provider Issues**

   **OpenAI:**
   - Verify API key in `.env`
   - Check API quota and billing
   - Try different model (e.g., gpt-3.5-turbo)

   **Ollama:**
   - Check if Ollama is running: `ollama list`
   - Pull a model: `ollama pull llama2:7b`
   - Verify server: `curl http://localhost:11434/api/tags`
   - Check Docker: `docker logs ollama-demo`

3. **Import Errors**
   - Run `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

4. **Docker Issues**
   - Ensure Docker is installed and running
   - Try `docker-compose down && docker-compose up -d`

### Getting Help

- Check the logs for detailed error messages
- Run tests: `python test_demo.py`
- Verify setup: `python quick_start.py`
