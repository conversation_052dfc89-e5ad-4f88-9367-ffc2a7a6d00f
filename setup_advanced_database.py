#!/usr/bin/env python3
"""
Advanced database setup with more realistic business data
"""

import random
import json
from datetime import datetime, timedelta
from clickhouse_client import clickhouse_client
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Advanced table schemas
ADVANCED_TABLES = {
    "companies": {
        "columns": [
            "id UInt32",
            "name String",
            "industry String",
            "country String",
            "founded_year UInt16",
            "employees UInt32",
            "revenue Float64",
            "created_at DateTime"
        ],
        "engine": "MergeTree()",
        "order_by": "id"
    },
    "users": {
        "columns": [
            "id UInt32",
            "email String",
            "name String",
            "age UInt8",
            "gender String",
            "country String",
            "city String",
            "signup_date Date",
            "last_login DateTime",
            "subscription_type String",
            "total_spent Float64"
        ],
        "engine": "MergeTree()",
        "order_by": "id"
    },
    "events": {
        "columns": [
            "id UInt64",
            "user_id UInt32",
            "event_type String",
            "event_data String",
            "timestamp DateTime",
            "session_id String",
            "device_type String",
            "browser String",
            "country String"
        ],
        "engine": "MergeTree()",
        "order_by": "timestamp"
    },
    "transactions": {
        "columns": [
            "id UInt64",
            "user_id UInt32",
            "amount Float64",
            "currency String",
            "transaction_type String",
            "status String",
            "created_at DateTime",
            "updated_at DateTime",
            "payment_method String",
            "merchant_category String"
        ],
        "engine": "MergeTree()",
        "order_by": "created_at"
    },
    "products": {
        "columns": [
            "id UInt32",
            "name String",
            "category String",
            "subcategory String",
            "price Float64",
            "cost Float64",
            "brand String",
            "rating Float32",
            "reviews_count UInt32",
            "in_stock UInt32",
            "created_at DateTime"
        ],
        "engine": "MergeTree()",
        "order_by": "id"
    },
    "orders": {
        "columns": [
            "id UInt64",
            "user_id UInt32",
            "product_id UInt32",
            "quantity UInt16",
            "unit_price Float64",
            "total_amount Float64",
            "discount Float64",
            "order_date DateTime",
            "shipping_date DateTime",
            "delivery_date DateTime",
            "status String",
            "shipping_country String"
        ],
        "engine": "MergeTree()",
        "order_by": "order_date"
    }
}

def create_database():
    """Create the demo database if it doesn't exist."""
    try:
        clickhouse_client.execute_command(f"CREATE DATABASE IF NOT EXISTS {clickhouse_client.config['database']}")
        logger.info(f"Database {clickhouse_client.config['database']} created/verified")
    except Exception as e:
        logger.error(f"Failed to create database: {e}")
        raise

def create_tables():
    """Create advanced tables."""
    for table_name, table_config in ADVANCED_TABLES.items():
        columns_def = ", ".join(table_config["columns"])
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS {table_name} (
            {columns_def}
        ) ENGINE = {table_config["engine"]}
        ORDER BY {table_config["order_by"]}
        """
        
        try:
            clickhouse_client.execute_command(create_sql)
            logger.info(f"Table {table_name} created successfully")
        except Exception as e:
            logger.error(f"Failed to create table {table_name}: {e}")
            raise

def generate_companies_data():
    """Generate realistic company data."""
    industries = ["Technology", "Finance", "Healthcare", "Retail", "Manufacturing", "Education", "Media", "Energy"]
    countries = ["USA", "China", "Germany", "Japan", "UK", "France", "India", "Canada", "Australia", "Brazil"]
    
    companies = []
    for i in range(1, 501):  # 500 companies
        company = {
            "id": i,
            "name": f"Company {i}",
            "industry": random.choice(industries),
            "country": random.choice(countries),
            "founded_year": random.randint(1950, 2023),
            "employees": random.randint(10, 100000),
            "revenue": round(random.uniform(100000, **********), 2),
            "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        companies.append(company)
    
    # Insert companies
    for company in companies:
        insert_sql = f"""
        INSERT INTO companies VALUES (
            {company['id']}, '{company['name']}', '{company['industry']}', 
            '{company['country']}', {company['founded_year']}, {company['employees']}, 
            {company['revenue']}, '{company['created_at']}'
        )
        """
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(companies)} companies")

def generate_users_data():
    """Generate realistic user data."""
    countries = ["USA", "UK", "Germany", "France", "Canada", "Australia", "Japan", "Brazil", "India", "China"]
    cities = ["New York", "London", "Berlin", "Paris", "Toronto", "Sydney", "Tokyo", "São Paulo", "Mumbai", "Beijing"]
    genders = ["Male", "Female", "Other"]
    subscription_types = ["Free", "Basic", "Premium", "Enterprise"]
    
    users = []
    for i in range(1, 10001):  # 10,000 users
        signup_date = datetime.now() - timedelta(days=random.randint(1, 1095))  # Last 3 years
        last_login = signup_date + timedelta(days=random.randint(0, 30))
        
        user = {
            "id": i,
            "email": f"user{i}@example.com",
            "name": f"User {i}",
            "age": random.randint(18, 80),
            "gender": random.choice(genders),
            "country": random.choice(countries),
            "city": random.choice(cities),
            "signup_date": signup_date.strftime('%Y-%m-%d'),
            "last_login": last_login.strftime('%Y-%m-%d %H:%M:%S'),
            "subscription_type": random.choice(subscription_types),
            "total_spent": round(random.uniform(0, 5000), 2)
        }
        users.append(user)
    
    # Batch insert users
    batch_size = 1000
    for i in range(0, len(users), batch_size):
        batch = users[i:i + batch_size]
        values = []
        for user in batch:
            values.append(f"({user['id']}, '{user['email']}', '{user['name']}', {user['age']}, "
                         f"'{user['gender']}', '{user['country']}', '{user['city']}', "
                         f"'{user['signup_date']}', '{user['last_login']}', "
                         f"'{user['subscription_type']}', {user['total_spent']})")
        
        insert_sql = f"INSERT INTO users VALUES {', '.join(values)}"
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(users)} users")

def generate_events_data():
    """Generate realistic event data."""
    event_types = ["page_view", "click", "purchase", "signup", "login", "logout", "search", "download"]
    devices = ["desktop", "mobile", "tablet"]
    browsers = ["Chrome", "Firefox", "Safari", "Edge", "Opera"]
    countries = ["USA", "UK", "Germany", "France", "Canada", "Australia", "Japan", "Brazil"]
    
    events = []
    event_id = 1
    
    # Generate events for random users over the last 30 days
    for day in range(30):
        date = datetime.now() - timedelta(days=day)
        events_per_day = random.randint(1000, 5000)
        
        for _ in range(events_per_day):
            event = {
                "id": event_id,
                "user_id": random.randint(1, 10000),
                "event_type": random.choice(event_types),
                "event_data": json.dumps({"page": f"/page{random.randint(1, 100)}", "value": random.randint(1, 1000)}),
                "timestamp": (date + timedelta(seconds=random.randint(0, 86400))).strftime('%Y-%m-%d %H:%M:%S'),
                "session_id": f"session_{random.randint(1, 100000)}",
                "device_type": random.choice(devices),
                "browser": random.choice(browsers),
                "country": random.choice(countries)
            }
            events.append(event)
            event_id += 1
    
    # Batch insert events
    batch_size = 1000
    for i in range(0, len(events), batch_size):
        batch = events[i:i + batch_size]
        values = []
        for event in batch:
            values.append(f"({event['id']}, {event['user_id']}, '{event['event_type']}', "
                         f"'{event['event_data']}', '{event['timestamp']}', '{event['session_id']}', "
                         f"'{event['device_type']}', '{event['browser']}', '{event['country']}')")
        
        insert_sql = f"INSERT INTO events VALUES {', '.join(values)}"
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(events)} events")

def generate_products_data():
    """Generate realistic product data."""
    categories = ["Electronics", "Clothing", "Books", "Home", "Sports", "Beauty", "Automotive", "Toys"]
    subcategories = {
        "Electronics": ["Smartphones", "Laptops", "Headphones", "Cameras"],
        "Clothing": ["Shirts", "Pants", "Shoes", "Accessories"],
        "Books": ["Fiction", "Non-fiction", "Educational", "Comics"],
        "Home": ["Furniture", "Kitchen", "Decor", "Garden"],
        "Sports": ["Fitness", "Outdoor", "Team Sports", "Water Sports"],
        "Beauty": ["Skincare", "Makeup", "Hair Care", "Fragrance"],
        "Automotive": ["Parts", "Accessories", "Tools", "Care"],
        "Toys": ["Educational", "Action", "Board Games", "Puzzles"]
    }
    brands = ["BrandA", "BrandB", "BrandC", "BrandD", "BrandE"]
    
    products = []
    for i in range(1, 1001):  # 1,000 products
        category = random.choice(categories)
        subcategory = random.choice(subcategories[category])
        cost = round(random.uniform(5, 500), 2)
        price = round(cost * random.uniform(1.2, 3.0), 2)  # Markup
        
        product = {
            "id": i,
            "name": f"{subcategory} Product {i}",
            "category": category,
            "subcategory": subcategory,
            "price": price,
            "cost": cost,
            "brand": random.choice(brands),
            "rating": round(random.uniform(1.0, 5.0), 1),
            "reviews_count": random.randint(0, 1000),
            "in_stock": random.randint(0, 500),
            "created_at": (datetime.now() - timedelta(days=random.randint(1, 365))).strftime('%Y-%m-%d %H:%M:%S')
        }
        products.append(product)
    
    # Batch insert products
    batch_size = 100
    for i in range(0, len(products), batch_size):
        batch = products[i:i + batch_size]
        values = []
        for product in batch:
            values.append(f"({product['id']}, '{product['name']}', '{product['category']}', "
                         f"'{product['subcategory']}', {product['price']}, {product['cost']}, "
                         f"'{product['brand']}', {product['rating']}, {product['reviews_count']}, "
                         f"{product['in_stock']}, '{product['created_at']}')")
        
        insert_sql = f"INSERT INTO products VALUES {', '.join(values)}"
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(products)} products")

def generate_orders_data():
    """Generate realistic order data."""
    statuses = ["pending", "processing", "shipped", "delivered", "cancelled"]
    countries = ["USA", "UK", "Germany", "France", "Canada", "Australia", "Japan", "Brazil"]
    
    orders = []
    order_id = 1
    
    # Generate orders for the last 6 months
    for day in range(180):
        date = datetime.now() - timedelta(days=day)
        orders_per_day = random.randint(50, 200)
        
        for _ in range(orders_per_day):
            user_id = random.randint(1, 10000)
            product_id = random.randint(1, 1000)
            quantity = random.randint(1, 5)
            unit_price = round(random.uniform(10, 500), 2)
            total_amount = round(quantity * unit_price, 2)
            discount = round(total_amount * random.uniform(0, 0.2), 2)
            
            order_date = date + timedelta(seconds=random.randint(0, 86400))
            shipping_date = order_date + timedelta(days=random.randint(1, 3))
            delivery_date = shipping_date + timedelta(days=random.randint(1, 7))
            
            order = {
                "id": order_id,
                "user_id": user_id,
                "product_id": product_id,
                "quantity": quantity,
                "unit_price": unit_price,
                "total_amount": total_amount,
                "discount": discount,
                "order_date": order_date.strftime('%Y-%m-%d %H:%M:%S'),
                "shipping_date": shipping_date.strftime('%Y-%m-%d %H:%M:%S'),
                "delivery_date": delivery_date.strftime('%Y-%m-%d %H:%M:%S'),
                "status": random.choice(statuses),
                "shipping_country": random.choice(countries)
            }
            orders.append(order)
            order_id += 1
    
    # Batch insert orders
    batch_size = 1000
    for i in range(0, len(orders), batch_size):
        batch = orders[i:i + batch_size]
        values = []
        for order in batch:
            values.append(f"({order['id']}, {order['user_id']}, {order['product_id']}, "
                         f"{order['quantity']}, {order['unit_price']}, {order['total_amount']}, "
                         f"{order['discount']}, '{order['order_date']}', '{order['shipping_date']}', "
                         f"'{order['delivery_date']}', '{order['status']}', '{order['shipping_country']}')")
        
        insert_sql = f"INSERT INTO orders VALUES {', '.join(values)}"
        clickhouse_client.execute_command(insert_sql)
    
    logger.info(f"Inserted {len(orders)} orders")

def main():
    """Main setup function."""
    logger.info("Starting advanced database setup...")
    
    try:
        create_database()
        create_tables()
        
        logger.info("Generating sample data...")
        generate_companies_data()
        generate_users_data()
        generate_products_data()
        generate_events_data()
        generate_orders_data()
        
        # Verify setup
        tables = clickhouse_client.get_tables()
        logger.info(f"Setup complete. Created tables: {tables}")
        
        # Show sample counts
        for table in tables:
            count_result = clickhouse_client.execute_query(f"SELECT COUNT(*) as count FROM {table}")
            count = count_result.iloc[0]['count']
            logger.info(f"Table {table}: {count:,} records")
            
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        raise
    finally:
        clickhouse_client.close()

if __name__ == "__main__":
    main()
