#!/usr/bin/env python3
"""
AutoGen + ClickHouse Web Service
Provides a web interface for AI-powered database queries
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Import our modules
from clickhouse_client import clickhouse_client
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import ModelInfo
from config import LLM_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Conversation history management
conversation_histories = {}  # session_id -> list of messages
MAX_HISTORY_LENGTH = 20  # Maximum number of messages to keep in history

# Database tool functions for AI agent
def execute_clickhouse_query(query: str) -> str:
    """
    Execute a ClickHouse query and return results as formatted string.

    Args:
        query: SQL query to execute

    Returns:
        Formatted query results or error message
    """
    try:
        # Validate query (basic security check)
        query_lower = query.lower().strip()
        if not query_lower.startswith('select'):
            return "Error: Only SELECT queries are allowed for security reasons."

        # Execute query
        result_df = clickhouse_client.execute_query(query)

        if result_df.empty:
            return "Query executed successfully but returned no results."

        # Format results
        if len(result_df) > 20:
            formatted_result = f"Query returned {len(result_df)} rows. Showing first 20:\n\n"
            formatted_result += result_df.head(20).to_string(index=False)
            formatted_result += f"\n\n... and {len(result_df) - 20} more rows."
        else:
            formatted_result = f"Query returned {len(result_df)} rows:\n\n"
            formatted_result += result_df.to_string(index=False)

        return formatted_result

    except Exception as e:
        error_msg = f"Query execution failed: {str(e)}"
        logger.error(error_msg)
        return error_msg

def get_database_schema() -> str:
    """Get database schema information."""
    try:
        tables = clickhouse_client.get_tables()
        schema_info = "Database Schema:\n\n"

        for table in tables:
            schema_info += f"Table: {table}\n"
            schema = clickhouse_client.get_table_schema(table)
            for column in schema:
                schema_info += f"  - {column['name']}: {column['type']}\n"
            schema_info += "\n"

        return schema_info
    except Exception as e:
        return f"Error retrieving schema: {str(e)}"

def get_table_sample(table_name: str, limit: int = 5) -> str:
    """Get sample data from a table."""
    try:
        sample_df = clickhouse_client.get_table_sample(table_name, limit)
        return f"Sample data from {table_name}:\n\n{sample_df.to_string(index=False)}"
    except Exception as e:
        return f"Error retrieving sample data: {str(e)}"

def get_conversation_history(session_id: str) -> List[Dict[str, str]]:
    """Get conversation history for a session."""
    return conversation_histories.get(session_id, [])

def add_to_conversation_history(session_id: str, role: str, content: str):
    """Add a message to conversation history."""
    if session_id not in conversation_histories:
        conversation_histories[session_id] = []

    conversation_histories[session_id].append({
        "role": role,
        "content": content,
        "timestamp": datetime.now().isoformat()
    })

    # Keep only the last MAX_HISTORY_LENGTH messages
    if len(conversation_histories[session_id]) > MAX_HISTORY_LENGTH:
        conversation_histories[session_id] = conversation_histories[session_id][-MAX_HISTORY_LENGTH:]

def format_conversation_context(session_id: str) -> str:
    """Format conversation history as context for the AI agent."""
    history = get_conversation_history(session_id)
    if not history:
        return ""

    context = "\n\nPrevious conversation context:\n"
    for msg in history[-6:]:  # Show last 6 messages for context
        role_label = "User" if msg["role"] == "user" else "Assistant"
        context += f"{role_label}: {msg['content'][:200]}...\n"

    context += "\nUse this context to provide relevant follow-up responses and reference previous queries when appropriate.\n"
    return context

# FastAPI app
app = FastAPI(
    title="AutoGen + ClickHouse Web Service",
    description="AI-powered database query interface",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class QueryRequest(BaseModel):
    question: str
    include_raw_data: bool = True
    session_id: Optional[str] = None  # For conversation context

class QueryResponse(BaseModel):
    answer: str
    raw_data: Optional[Dict[str, Any]] = None
    execution_time: float
    timestamp: str

class DatabaseInfo(BaseModel):
    tables: List[str]
    total_records: Dict[str, int]
    table_schemas: Dict[str, List[Dict[str, str]]]

class ConnectionManager:
    """WebSocket connection manager for real-time updates"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

def get_model_client():
    """Get the model client with proper configuration."""
    model_name = LLM_CONFIG["model"]
    
    # Check if it's a standard OpenAI model
    standard_openai_models = [
        "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
        "gpt-3.5-turbo", "gpt-3.5-turbo-16k"
    ]
    
    if model_name in standard_openai_models:
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url")
        )
    else:
        model_info = ModelInfo(
            family="openai",
            vision=False,
            function_calling=True,
            json_output=True,
            structured_output=False
        )
        
        return OpenAIChatCompletionClient(
            model=model_name,
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            model_info=model_info
        )

def get_database_info() -> Dict[str, Any]:
    """Get comprehensive database information."""
    try:
        tables = clickhouse_client.get_tables()
        
        # Get table info
        table_info = {}
        schema_info = {}
        
        for table in tables:
            # Get record count
            count_result = clickhouse_client.execute_query(f"SELECT COUNT(*) as count FROM {table}")
            table_info[table] = int(count_result.iloc[0]['count'])
            
            # Get schema
            schema = clickhouse_client.get_table_schema(table)
            schema_info[table] = schema
        
        return {
            "tables": tables,
            "total_records": table_info,
            "table_schemas": schema_info
        }
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        return {"error": str(e)}

def execute_safe_query(query: str) -> Dict[str, Any]:
    """Execute a query safely and return formatted results."""
    try:
        # Basic security check
        query_lower = query.lower().strip()
        dangerous_keywords = ['drop', 'delete', 'insert', 'update', 'create', 'alter', 'truncate']
        
        if any(keyword in query_lower for keyword in dangerous_keywords):
            return {"error": "Only SELECT queries are allowed for security reasons."}
        
        if not query_lower.startswith('select'):
            return {"error": "Only SELECT queries are allowed."}
        
        # Execute query
        result_df = clickhouse_client.execute_query(query)
        
        if result_df.empty:
            return {"data": [], "columns": [], "row_count": 0}
        
        # Convert to JSON-serializable format
        return {
            "data": result_df.to_dict('records'),
            "columns": list(result_df.columns),
            "row_count": len(result_df),
            "preview": result_df.head(10).to_dict('records') if len(result_df) > 10 else result_df.to_dict('records')
        }
        
    except Exception as e:
        logger.error(f"Query execution error: {e}")
        return {"error": str(e)}

async def create_ai_analyst(database_info: Dict[str, Any], session_id: str = None) -> AssistantAgent:
    """Create an AI analyst with current database information and SQL execution tools."""
    model_client = get_model_client()

    # Format database information for the system message
    db_summary = "Database Information:\n\n"

    for table, count in database_info.get("total_records", {}).items():
        db_summary += f"Table: {table} ({count:,} records)\n"

        # Add schema info
        schema = database_info.get("table_schemas", {}).get(table, [])
        for column in schema[:5]:  # Show first 5 columns
            db_summary += f"  - {column['name']}: {column['type']}\n"
        if len(schema) > 5:
            db_summary += f"  ... and {len(schema) - 5} more columns\n"
        db_summary += "\n"

    # Add conversation context if available
    conversation_context = ""
    if session_id:
        conversation_context = format_conversation_context(session_id)

    system_message = f"""
    You are an AI Data Analyst with access to a ClickHouse database. Your role is to:

    1. Answer business questions using the available data
    2. Execute SQL queries to retrieve actual data
    3. Provide insights and recommendations based on data patterns
    4. Explain complex data relationships in simple terms

    {db_summary}

    Available tools:
    - execute_clickhouse_query: Execute SELECT queries to get actual data
    - get_database_schema: Get detailed schema information
    - get_table_sample: Get sample data from specific tables

    When users ask questions:
    1. First understand what they want to know
    2. Break down complex questions into multiple data requirements
    3. Execute ALL necessary SQL queries to get comprehensive data
    4. For questions requiring multiple data sets (e.g., "sales by region AND product categories"), execute separate queries for each data set
    5. Analyze and explain ALL results with specific data points
    6. Provide actionable insights and recommendations based on all the data

    CRITICAL INSTRUCTIONS:
    - For comprehensive questions, execute MULTIPLE queries to provide complete answers
    - Don't stop after one query - continue executing additional queries until you have all the data needed
    - When asked for "X AND Y", execute separate queries for X and for Y
    - Pay attention to table schemas: sales table has 'region', customers table has 'country'
    - Always execute queries to provide real data in your responses
    - Use only SELECT queries for security

    EXAMPLE: If asked "Show me sales by region AND product categories":
    1. Execute: SELECT region, SUM(total_amount) FROM sales GROUP BY region
    2. Execute: SELECT p.category, SUM(s.total_amount) FROM sales s JOIN products p ON s.product_id = p.id GROUP BY p.category
    3. Analyze both results and provide comprehensive insights

    {conversation_context}
    """

    # Define tools for the agent
    tools = [
        execute_clickhouse_query,
        get_database_schema,
        get_table_sample
    ]

    return AssistantAgent(
        name="DataAnalyst",
        model_client=model_client,
        system_message=system_message,
        tools=tools
    )

# API Routes

@app.get("/", response_class=HTMLResponse)
async def get_homepage():
    """Serve the main web interface."""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Ai data analysis</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    </head>
    <body class="bg-gray-100 min-h-screen">
        <div class="container mx-auto px-4 py-8">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    🤖 AI data analysis
                </h1>
                <p class="text-gray-600">Ask questions about your data in natural language</p>
            </div>
            
            <!-- Database Info -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">📊 Database Overview</h2>
                <div id="db-loading" class="text-gray-500">Loading database information...</div>
                <div id="db-info" class="grid grid-cols-1 md:grid-cols-3 gap-4" style="display: none;">
                    <!-- Database tables will be populated here -->
                </div>
            </div>
            
            <!-- Query Interface -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">💬 Ask a Question</h2>
                
                <!-- Example Questions -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2">Example questions:</p>
                    <div id="example-questions" class="flex flex-wrap gap-2">
                        <!-- Example questions will be populated here -->
                    </div>
                </div>

                <!-- Query Input -->
                <div class="flex gap-4 mb-4">
                    <input
                        type="text"
                        id="question-input"
                        placeholder="Ask a question about your data..."
                        class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                    <button
                        id="ask-button"
                        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                        <span id="button-text">Ask</span>
                    </button>
                </div>

                <!-- Include Raw Data Option -->
                <label class="flex items-center text-sm text-gray-600">
                    <input type="checkbox" id="include-raw-data" class="mr-2">
                    Include raw data in response
                </label>
            </div>
            
            <!-- Results -->
            <div id="results-container" class="space-y-4" style="display: none;">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 Query Results</h2>
                <div id="results-list">
                    <!-- Results will be populated here -->
                </div>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="bg-white rounded-lg shadow-md p-6 text-center" style="display: none;">
                <div class="flex items-center justify-center space-x-2">
                    <svg class="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-gray-600">🤖 AI is analyzing your question...</span>
                </div>
                <p class="text-sm text-gray-500 mt-2">This may take a few seconds</p>
            </div>

            <!-- Error State -->
            <div id="error-state" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4" style="display: none;">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Error</h3>
                        <div id="error-message" class="mt-2 text-sm text-red-700"></div>
                        <button id="dismiss-error" class="mt-2 text-sm text-red-600 hover:text-red-500">Dismiss</button>
                    </div>
                </div>
            </div>

            <!-- No Results Message -->
            <div id="no-results" class="text-center text-gray-500 py-8">
                <div class="text-6xl mb-4">🤖</div>
                <h3 class="text-lg font-medium mb-2">Ready to analyze your data!</h3>
                <p>Ask a question about your ClickHouse database to get started.</p>
            </div>
        </div>
        
        <script>
            // Global variables
            let loading = false;
            let results = [];
            let dbInfo = null;
            let sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const exampleQuestions = [
                "What tables do we have?",
                "Show me sales by region AND product categories",
                "What are our top selling regions?",
                "Show me the products sold in those regions",
                "Analyze customer demographics and sales performance"
            ];

            // Initialize when document is ready
            $(document).ready(function() {
                console.log('Document ready, initializing...');
                window.loadDatabaseInfo();
                window.setupEventHandlers();
                window.populateExampleQuestions();
            });

            // Load database information (global)
            window.loadDatabaseInfo = async function() {
                try {
                    console.log('Loading database info...');
                    const response = await fetch('/api/database-info');
                    dbInfo = await response.json();
                    console.log('Database info loaded:', dbInfo);
                    window.displayDatabaseInfo();
                } catch (error) {
                    console.error('Error loading database info:', error);
                }
            }

            // Display database information (global)
            window.displayDatabaseInfo = function() {
                if (!dbInfo) return;

                $('#db-loading').hide();

                const dbInfoContainer = $('#db-info');
                dbInfoContainer.empty();

                if (dbInfo.tables && dbInfo.tables.length > 0) {
                    dbInfo.tables.forEach(table => {
                        const recordCount = dbInfo.total_records ? dbInfo.total_records[table] || 0 : 0;
                        const tableCard = `
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-blue-800">${table}</h3>
                                <p class="text-sm text-blue-600">${recordCount.toLocaleString()} records</p>
                            </div>
                        `;
                        dbInfoContainer.append(tableCard);
                    });
                }

                dbInfoContainer.show();
            }

            // Populate example questions (global)
            window.populateExampleQuestions = function() {
                const container = $('#example-questions');
                container.empty();

                exampleQuestions.forEach(question => {
                    const button = `
                        <button class="example-question text-xs bg-gray-200 hover:bg-gray-300 px-3 py-1 rounded-full transition-colors"
                                data-question="${question}">
                            ${question}
                        </button>
                    `;
                    container.append(button);
                });
            }

            // Setup event handlers (global)
            window.setupEventHandlers = function() {
                // Ask button click
                $('#ask-button').click(function() {
                    window.submitQuery();
                });

                // Enter key in input
                $('#question-input').keypress(function(e) {
                    if (e.which === 13) { // Enter key
                        window.submitQuery();
                    }
                });

                // Example question clicks
                $(document).on('click', '.example-question', function() {
                    const question = $(this).data('question');
                    $('#question-input').val(question);
                });

                // Dismiss error
                $('#dismiss-error').click(function() {
                    hideError();
                });
            }

            // Submit query function (global)
            window.submitQuery = async function() {
                console.log('submitQuery called');

                if (loading) {
                    console.log('Already loading, returning');
                    return;
                }

                const question = $('#question-input').val().trim();
                if (!question) {
                    showError('Please enter a question');
                    return;
                }

                console.log('Starting query...', question);
                setLoading(true);
                hideError();

                try {
                    console.log('Sending request to /api/query');
                    const includeRawData = $('#include-raw-data').is(':checked');

                    const response = await fetch('/api/query', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            question: question,
                            include_raw_data: includeRawData,
                            session_id: sessionId
                        })
                    });

                    console.log('Response status:', response.status);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('Response error:', errorText);
                        throw new Error(`Server error (${response.status}): ${errorText}`);
                    }

                    const result = await response.json();
                    console.log('Query result:', result);

                    // Store the question with the result
                    result.question = question;

                    // Add to results
                    results.unshift(result);

                    // Clear the input
                    $('#question-input').val('');

                    // Display results
                    displayResults();

                    console.log('Query completed successfully, results count:', results.length);

                } catch (error) {
                    console.error('Error submitting query:', error);
                    showError(`Failed to process query: ${error.message}`);
                } finally {
                    setLoading(false);
                    console.log('Loading set to false');
                }
            }

            // Set loading state
            function setLoading(isLoading) {
                loading = isLoading;

                if (isLoading) {
                    $('#ask-button').prop('disabled', true);
                    $('#question-input').prop('disabled', true);
                    $('#button-text').text('Processing...');
                    $('#loading-state').show();
                    $('#no-results').hide();
                } else {
                    $('#ask-button').prop('disabled', false);
                    $('#question-input').prop('disabled', false);
                    $('#button-text').text('Ask');
                    $('#loading-state').hide();
                }
            }

            // Show error
            function showError(message) {
                $('#error-message').text(message);
                $('#error-state').show();
            }

            // Hide error
            function hideError() {
                $('#error-state').hide();
            }

            // Display results
            function displayResults() {
                if (results.length === 0) {
                    $('#results-container').hide();
                    $('#no-results').show();
                    return;
                }

                $('#no-results').hide();

                const resultsContainer = $('#results-list');
                resultsContainer.empty();

                results.forEach((result, index) => {
                    const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';
                    const formattedAnswer = formatAnswer(result.answer || 'No answer received');

                    let rawDataSection = '';
                    if (result.raw_data) {
                        rawDataSection = `
                            <div class="mt-4">
                                <h4 class="font-semibold text-gray-800 mb-2">📊 Raw Data:</h4>
                                <div class="bg-gray-100 p-3 rounded overflow-auto max-h-40">
                                    <pre class="text-xs">${JSON.stringify(result.raw_data, null, 2)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    const resultCard = `
                        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                            <div class="flex justify-between items-start mb-4">
                                <h3 class="font-semibold text-gray-800">Question:</h3>
                                <span class="text-xs text-gray-500">${timestamp}</span>
                            </div>
                            <p class="text-gray-700 mb-4 bg-blue-50 p-3 rounded border-l-4 border-blue-300">${result.question}</p>

                            <h3 class="font-semibold text-gray-800 mb-2">🤖 AI Answer:</h3>
                            <div class="prose max-w-none mb-4 bg-gray-50 p-4 rounded">${formattedAnswer}</div>

                            ${rawDataSection}

                            <div class="text-xs text-gray-500 mt-2 flex justify-between">
                                <span>Execution time: ${(result.execution_time || 0).toFixed(2)}s</span>
                                <span>Status: <span class="text-green-600 font-semibold">✅ Complete</span></span>
                            </div>
                        </div>
                    `;

                    resultsContainer.append(resultCard);
                });

                $('#results-container').show();
            }

            // Format answer with simple markdown-like formatting
            function formatAnswer(answer) {
                return answer
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/\\n/g, '<br>');
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/database-info", response_model=DatabaseInfo)
async def get_database_info_api():
    """Get database information."""
    try:
        info = get_database_info()
        if "error" in info:
            raise HTTPException(status_code=500, detail=info["error"])
        
        return DatabaseInfo(**info)
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/query", response_model=QueryResponse)
async def query_data(request: QueryRequest):
    """Process a natural language query with conversation context."""
    start_time = datetime.now()

    try:
        # Generate session ID if not provided
        session_id = request.session_id or f"session_{datetime.now().timestamp()}"

        # Get database info
        db_info = get_database_info()
        if "error" in db_info:
            raise HTTPException(status_code=500, detail=db_info["error"])

        # Add user question to conversation history
        add_to_conversation_history(session_id, "user", request.question)

        # Create AI analyst with conversation context
        analyst = await create_ai_analyst(db_info, session_id)

        # Process the question
        response = await analyst.run(task=request.question)
        answer = response.messages[-1].content

        # Add assistant response to conversation history
        add_to_conversation_history(session_id, "assistant", answer)

        # Get raw data if requested
        raw_data = None
        if request.include_raw_data:
            raw_data = {
                "database_info": db_info,
                "question_processed": request.question,
                "session_id": session_id,
                "conversation_history": get_conversation_history(session_id)
            }

        execution_time = (datetime.now() - start_time).total_seconds()

        return QueryResponse(
            answer=answer,
            raw_data=raw_data,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates."""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for now - can be extended for real-time features
            await manager.send_personal_message(f"Echo: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        tables = clickhouse_client.get_tables()
        return {
            "status": "healthy",
            "database_connected": True,
            "tables_count": len(tables),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

if __name__ == "__main__":
    uvicorn.run(
        "web_service:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
