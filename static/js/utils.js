// Utility functions for AI Data Analysis App
// Version: 2025-08-01-14:20 - Data display fix
console.log('🔄 Utils.js loaded - Version: 2025-08-01-14:20');

// UI State Management
function setLoading(isLoading) {
    const askButton = document.getElementById('ask-button');
    const questionInput = document.getElementById('question-input');
    const buttonText = document.getElementById('button-text');
    const loadingState = document.getElementById('loading-state');
    const noResults = document.getElementById('no-results');

    if (isLoading) {
        askButton.disabled = true;
        questionInput.disabled = true;

        // Update button text based on enabled features
        const optimizePrompt = document.getElementById('optimize-prompt').checked;
        const enableTaskDecomposition = document.getElementById('enable-task-decomposition').checked;
        const generateCharts = document.getElementById('generate-charts').checked;

        let text = 'Processing...';
        if (optimizePrompt && enableTaskDecomposition && generateCharts) {
            text = 'Optimizing & Analyzing...';
        } else if (optimizePrompt && enableTaskDecomposition) {
            text = 'Optimizing & Decomposing...';
        } else if (generateCharts) {
            text = 'Analyzing & Charting...';
        } else if (optimizePrompt) {
            text = 'Optimizing...';
        } else if (enableTaskDecomposition) {
            text = 'Decomposing...';
        }

        buttonText.textContent = text;
        loadingState.style.display = 'block';
        noResults.style.display = 'none';
    } else {
        askButton.disabled = false;
        questionInput.disabled = false;
        buttonText.textContent = 'Ask';
        loadingState.style.display = 'none';
    }
}

function setSQLLoading(isLoading) {
    const executeButton = document.getElementById('execute-sql-button');
    const sqlInput = document.getElementById('sql-input');
    const sqlButtonText = document.getElementById('sql-button-text');

    if (isLoading) {
        executeButton.disabled = true;
        sqlInput.disabled = true;
        sqlButtonText.textContent = 'Executing...';
    } else {
        executeButton.disabled = false;
        sqlInput.disabled = false;
        sqlButtonText.textContent = 'Execute SQL';
    }
}

// Error handling
function showError(message) {
    const errorState = document.getElementById('error-state');
    const errorMessage = document.getElementById('error-message');
    errorMessage.textContent = message;
    errorState.style.display = 'block';
}

function hideError() {
    const errorState = document.getElementById('error-state');
    errorState.style.display = 'none';
}

// Results display
function displayResults(results) {
    if (results.length === 0) {
        document.getElementById('results-container').style.display = 'none';
        document.getElementById('no-results').style.display = 'block';
        return;
    }

    document.getElementById('no-results').style.display = 'none';

    const resultsContainer = document.getElementById('results-list');
    resultsContainer.innerHTML = '';

    results.forEach((result, index) => {
        const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';
        const formattedAnswer = formatAnswer(result.answer || 'No answer received');

        let rawDataSection = '';
        if (result.data && result.data.length > 0) {
            // Debug: log the data structure
            console.log('🔍 Creating table for data:', result.data);
            console.log('🔍 Data type:', typeof result.data);
            console.log('🔍 Data length:', result.data.length);
            console.log('🔍 First item:', result.data[0]);

            // Create a proper data table
            console.log('🔍 About to create table with data:', result.data);
            let tableHtml = createSimpleTable(result.data);  // 直接使用简单版本
            console.log('🔍 Generated tableHtml:', tableHtml);
            console.log('🔍 tableHtml length:', tableHtml ? tableHtml.length : 'undefined');

            rawDataSection = `
                <div class="mt-6">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <i class="material-icons text-primary-600 mr-2">table_view</i>
                            <h4 class="subtitle-1 font-medium text-gray-800">Query Results</h4>
                            <span class="ml-2 chip chip-info">${result.data.length} rows</span>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="downloadQueryData(${index}, 'csv')"
                                    class="export-btn-csv flex items-center">
                                <i class="material-icons text-sm mr-1">file_download</i>
                                CSV
                            </button>
                            <button onclick="downloadQueryData(${index}, 'json')"
                                    class="export-btn-json flex items-center">
                                <i class="material-icons text-sm mr-1">code</i>
                                JSON
                            </button>
                        </div>
                    </div>
                    ${tableHtml}
                </div>
            `;
        } else if (result.sql) {
            // Show SQL even if no data returned
            rawDataSection = `
                <div class="mt-6">
                    <div class="flex items-center mb-4">
                        <i class="material-icons text-primary-600 mr-2">code</i>
                        <h4 class="subtitle-1 font-medium text-gray-800">SQL Query</h4>
                    </div>
                    <div class="bg-gray-900 text-green-400 p-4 rounded-xl font-mono text-sm">
                        ${result.sql}
                    </div>
                    <p class="text-gray-500 text-sm mt-2">No data returned from this query.</p>
                </div>
            `;
        }

        const resultCard = document.createElement('div');
        resultCard.className = 'card border-left-accent fade-in elevation-2 hover:elevation-3';
        resultCard.innerHTML = `
            <div class="flex justify-between items-start mb-6">
                <div class="flex items-center">
                    <i class="material-icons text-primary-600 mr-3">help_outline</i>
                    <h3 class="subtitle-1 font-medium text-gray-800">Question</h3>
                </div>
                <div class="flex items-center text-gray-400">
                    <i class="material-icons text-sm mr-1">schedule</i>
                    <span class="caption">${timestamp}</span>
                </div>
            </div>
            <div class="bg-primary-50 p-4 rounded-xl border-l-4 border-primary-500 mb-6">
                <p class="body-1 text-gray-700">${result.question}</p>
            </div>

            <div class="flex items-center mb-4">
                <i class="material-icons text-success-600 mr-3">psychology</i>
                <h3 class="subtitle-1 font-medium text-gray-800">AI Answer</h3>
            </div>
            <div class="prose max-w-none mb-6 bg-gray-50 p-6 rounded-xl">${formattedAnswer}</div>

            ${rawDataSection}

            <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-100">
                <div class="flex items-center text-gray-500">
                    <i class="material-icons text-sm mr-2">timer</i>
                    <span class="caption">Execution time: ${(result.execution_time || 0).toFixed(2)}s</span>
                </div>
                <div class="flex items-center">
                    <div class="chip chip-success">
                        <i class="material-icons text-sm mr-1">check_circle</i>
                        Complete
                    </div>
                </div>
            </div>
        `;

        resultsContainer.appendChild(resultCard);
    });

    document.getElementById('results-container').style.display = 'block';
}

function createDataTable(data) {
    console.log('📊 createDataTable called with:', data);

    try {
        console.log('📊 Data type:', typeof data);
        console.log('📊 Is array:', Array.isArray(data));
        console.log('📊 Data length:', data ? data.length : 'undefined');

        // More robust data validation
        if (!data) {
            console.log('❌ Data is null or undefined');
            return '<p class="text-gray-500 text-center py-4">No data provided</p>';
        }

        if (!Array.isArray(data)) {
            console.log('❌ Data is not an array, type:', typeof data);
            return '<p class="text-gray-500 text-center py-4">Data is not in array format</p>';
        }

        if (data.length === 0) {
            console.log('❌ Data array is empty');
            return '<p class="text-gray-500 text-center py-4">No data to display</p>';
        }

        // Get column names from first row
        const firstRow = data[0];
        console.log('📊 First row:', firstRow);
        console.log('📊 First row type:', typeof firstRow);

        if (!firstRow) {
            console.log('❌ First row is null or undefined');
            return '<p class="text-gray-500 text-center py-4">Invalid data: first row is empty</p>';
        }

        if (typeof firstRow !== 'object') {
            console.log('❌ First row is not an object, type:', typeof firstRow);
            return '<p class="text-gray-500 text-center py-4">Invalid data format: expected object</p>';
        }

        const columns = Object.keys(firstRow);
        console.log('📊 Columns:', columns);

        if (columns.length === 0) {
            console.log('❌ No columns found in first row');
            return '<p class="text-gray-500 text-center py-4">No columns found in data</p>';
        }

        console.log('✅ Data validation passed, generating table...');

    // Limit rows for display (show first 10 rows)
    const displayData = data.slice(0, 10);
    const hasMoreRows = data.length > 10;

    let tableHtml = `
        <div class="overflow-x-auto bg-white rounded-xl border border-gray-200 shadow-sm">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        ${columns.map(col => `
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                ${col}
                            </th>
                        `).join('')}
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ${displayData.map((row, index) => `
                        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors">
                            ${columns.map(col => `
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${formatCellValue(row[col])}
                                </td>
                            `).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    if (hasMoreRows) {
        tableHtml += `
            <div class="mt-3 text-center">
                <p class="text-gray-500 text-sm">
                    Showing first 10 rows of ${data.length} total rows
                    <button onclick="showAllRows(this)" class="text-blue-600 hover:text-blue-800 ml-2">
                        Show all
                    </button>
                </p>
            </div>
        `;
    }

        console.log('✅ Table generation completed');
        console.log('📊 Final tableHtml length:', tableHtml.length);
        console.log('📊 Final tableHtml preview:', tableHtml.substring(0, 200) + '...');

        return tableHtml;

    } catch (error) {
        console.error('❌ Error in createDataTable:', error);
        return `<p class="text-red-500 text-center py-4">Error generating table: ${error.message}</p>`;
    }
}

// Simple fallback table generator
function createSimpleTable(data) {
    console.log('📊 Creating simple table for:', data);
    console.log('📊 Data type:', typeof data, 'Array:', Array.isArray(data), 'Length:', data?.length);

    if (!data) {
        console.log('❌ No data provided');
        return '<p class="text-red-500 text-center py-4">No data provided</p>';
    }

    if (!Array.isArray(data)) {
        console.log('❌ Data is not array');
        return '<p class="text-red-500 text-center py-4">Data is not an array</p>';
    }

    if (data.length === 0) {
        console.log('❌ Empty data array');
        return '<p class="text-red-500 text-center py-4">Empty data array</p>';
    }

    const firstRow = data[0];
    console.log('📊 First row:', firstRow);

    if (!firstRow) {
        console.log('❌ First row is null');
        return '<p class="text-red-500 text-center py-4">First row is null</p>';
    }

    if (typeof firstRow !== 'object') {
        console.log('❌ First row is not object');
        return '<p class="text-red-500 text-center py-4">First row is not an object</p>';
    }

    const columns = Object.keys(firstRow);
    console.log('📊 Columns:', columns);

    if (columns.length === 0) {
        console.log('❌ No columns found');
        return '<p class="text-red-500 text-center py-4">No columns found</p>';
    }

    console.log('✅ Starting table generation...');

    // 最简单的表格HTML
    let html = '<div class="bg-white rounded p-4"><table class="w-full border">';

    // 表头
    html += '<thead><tr>';
    for (const col of columns) {
        html += `<th class="border p-2 bg-gray-100">${col}</th>`;
    }
    html += '</tr></thead>';

    // 数据行
    html += '<tbody>';
    for (let i = 0; i < Math.min(data.length, 10); i++) {
        const row = data[i];
        html += '<tr>';
        for (const col of columns) {
            const value = row[col] || '';
            html += `<td class="border p-2">${value}</td>`;
        }
        html += '</tr>';
    }
    html += '</tbody></table>';

    if (data.length > 10) {
        html += `<p class="text-gray-500 text-sm mt-2">Showing 10 of ${data.length} rows</p>`;
    }

    html += '</div>';

    console.log('✅ Table HTML generated, length:', html.length);
    return html;
}

function formatCellValue(value) {
    if (value === null || value === undefined) {
        return '<span class="text-gray-400 italic">null</span>';
    }

    if (typeof value === 'boolean') {
        return value ? '<span class="text-green-600">true</span>' : '<span class="text-red-600">false</span>';
    }

    if (typeof value === 'number') {
        return `<span class="font-mono">${value}</span>`;
    }

    if (typeof value === 'string' && value.length > 50) {
        return `<span title="${value}">${value.substring(0, 50)}...</span>`;
    }

    return String(value);
}

function showAllRows(button) {
    // This function would expand the table to show all rows
    // For now, just show a message
    button.textContent = 'Feature coming soon';
    button.disabled = true;
}

// Fix export function to work with new data structure
function exportQueryData(index, format) {
    console.log('Exporting data:', index, format);

    // Get the result data from the global results array
    if (window.app && window.app.results && window.app.results[index]) {
        const result = window.app.results[index];
        const data = result.data;

        if (!data || data.length === 0) {
            showError('No data to export');
            return;
        }

        try {
            let content = '';
            let filename = '';
            let mimeType = '';

            if (format === 'csv') {
                content = convertToCSV(data);
                filename = `query_result_${Date.now()}.csv`;
                mimeType = 'text/csv';
            } else if (format === 'json') {
                content = JSON.stringify(data, null, 2);
                filename = `query_result_${Date.now()}.json`;
                mimeType = 'application/json';
            }

            // Create and trigger download
            const blob = new Blob([content], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('Export completed:', filename);

        } catch (error) {
            console.error('Export error:', error);
            showError(`Export failed: ${error.message}`);
        }
    } else {
        showError('No data found to export');
    }
}

function convertToCSV(data) {
    if (!data || data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row =>
            headers.map(header => {
                const value = row[header];
                // Escape quotes and wrap in quotes if contains comma or quote
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            }).join(',')
        )
    ].join('\n');

    return csvContent;
}

// New download function using server API
async function downloadQueryData(index, format) {
    console.log('📥 Downloading data:', index, format);

    try {
        // Get the result data from the global results array
        if (!window.app || !window.app.results || !window.app.results[index]) {
            showError('No data found to download');
            return;
        }

        const result = window.app.results[index];
        const sql = result.sql;

        if (!sql) {
            showError('No SQL query found for this result');
            return;
        }

        // Show loading state
        let button = null;
        let originalText = '';

        // Try to find the button that was clicked
        const buttons = document.querySelectorAll(`button[onclick*="downloadQueryData(${index}, '${format}')"]`);
        if (buttons.length > 0) {
            button = buttons[0];
            originalText = button.innerHTML;
            button.innerHTML = '<i class="material-icons text-sm mr-1 animate-spin">refresh</i>Loading...';
            button.disabled = true;
        }

        // Make API request to download data
        const response = await fetch('/api/download-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                sql: sql,
                format: format,
                filename: `query_result_${Date.now()}.${format}`
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || `Download failed: ${response.status}`);
        }

        // Get the filename from response headers
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `query_result_${Date.now()}.${format}`;
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename=(.+)/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // Create blob and download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('✅ Download completed:', filename);

        // Show success message
        showSuccess(`Downloaded ${filename} successfully!`);

    } catch (error) {
        console.error('❌ Download error:', error);
        showError(`Download failed: ${error.message}`);
    } finally {
        // Restore button state
        if (button) {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }
}

// Helper function to show success message
function showSuccess(message) {
    // Create or update success notification
    let successElement = document.getElementById('success-notification');
    if (!successElement) {
        successElement = document.createElement('div');
        successElement.id = 'success-notification';
        successElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        document.body.appendChild(successElement);
    }

    successElement.textContent = message;
    successElement.classList.remove('translate-x-full');

    // Auto hide after 3 seconds
    setTimeout(() => {
        successElement.classList.add('translate-x-full');
    }, 3000);
}

function displaySQLResults(result) {
    // Create or update SQL results section
    let sqlResultsSection = document.getElementById('sql-results-section');
    const container = document.getElementById('sql-results-container');
    container.innerHTML = '';

    // Show execution info
    const executionInfo = document.createElement('div');
    executionInfo.className = 'alert-success mb-4';
    executionInfo.innerHTML = `
        <p class="text-sm text-green-800">
            <strong>✅ ${result.message}</strong>
        </p>
        <p class="text-xs text-green-600 mt-1">
            Execution time: ${result.execution_time.toFixed(3)}s | Rows: ${result.row_count}
        </p>
    `;
    container.appendChild(executionInfo);

    // Show SQL query
    const sqlDisplay = document.createElement('div');
    sqlDisplay.className = 'mb-4';
    sqlDisplay.innerHTML = `
        <h4 class="text-md font-medium mb-2">📝 Executed SQL:</h4>
        <pre class="code-block"><code>${result.sql}</code></pre>
    `;
    container.appendChild(sqlDisplay);

    // Show data if available
    if (result.data && result.data.length > 0) {
        const dataSection = document.createElement('div');
        dataSection.className = 'mb-4';
        dataSection.innerHTML = `
            <h4 class="text-md font-medium mb-2">📊 Results:</h4>
            ${createDataTable(result.columns, result.data)}
        `;
        container.appendChild(dataSection);
    }

    sqlResultsSection.style.display = 'block';
    sqlResultsSection.scrollIntoView({ behavior: 'smooth' });
}

function createDataTable(columns, data) {
    if (!columns || !data || data.length === 0) {
        return '<p class="text-gray-500">No data to display</p>';
    }

    const table = document.createElement('div');
    table.className = 'overflow-x-auto';
    
    let tableHtml = '<table class="data-table">';
    
    // Header
    tableHtml += '<thead><tr>';
    columns.forEach(col => {
        tableHtml += `<th>${col}</th>`;
    });
    tableHtml += '</tr></thead>';

    // Body (limit to first 100 rows for display)
    tableHtml += '<tbody>';
    const displayData = data.slice(0, 100);
    displayData.forEach((row, index) => {
        tableHtml += '<tr>';
        columns.forEach(col => {
            const value = row[col] !== null && row[col] !== undefined ? row[col] : '';
            tableHtml += `<td>${value}</td>`;
        });
        tableHtml += '</tr>';
    });
    tableHtml += '</tbody></table>';

    if (data.length > 100) {
        tableHtml += `<p class="text-sm text-gray-500 mt-2">Showing first 100 rows of ${data.length} total rows.</p>`;
    }

    table.innerHTML = tableHtml;
    return table.outerHTML;
}

// Text formatting
function formatAnswer(answer) {
    return answer
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
}

// File download
function downloadFile(content, filename, format) {
    const blob = new Blob([content], { 
        type: format === 'csv' ? 'text/csv' : 'application/json' 
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// Export functions
function exportQueryData(resultIndex, format) {
    const app = window.app;
    const result = app.results[resultIndex];
    if (!result || !result.raw_data) {
        showError('No data available for export');
        return;
    }

    try {
        let content = '';
        let filename = '';

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

        if (format === 'csv') {
            content = convertToCSV(result.raw_data);
            filename = `query_result_${timestamp}.csv`;
        } else if (format === 'json') {
            content = JSON.stringify(result.raw_data, null, 2);
            filename = `query_result_${timestamp}.json`;
        }

        if (content) {
            downloadFile(content, filename, format);
        }
    } catch (error) {
        console.error('Export error:', error);
        showError(`Export failed: ${error.message}`);
    }
}

function convertToCSV(data) {
    let csvData = [];
    
    // Look for database_info tables
    if (data.database_info && data.database_info.table_schemas) {
        const schemas = data.database_info.table_schemas;
        csvData.push(['Table', 'Column', 'Type', 'Comment']);
        
        Object.keys(schemas).forEach(tableName => {
            schemas[tableName].forEach(column => {
                csvData.push([
                    tableName,
                    column.name || '',
                    column.type || '',
                    column.comment || ''
                ]);
            });
        });
    } else {
        // Generic object to CSV conversion
        const flatData = flattenObject(data);
        csvData.push(['Key', 'Value']);
        Object.keys(flatData).forEach(key => {
            csvData.push([key, String(flatData[key])]);
        });
    }

    // Convert array to CSV string
    return csvData.map(row => 
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
    ).join('\n');
}

function flattenObject(obj, prefix = '') {
    const flattened = {};
    
    Object.keys(obj).forEach(key => {
        const value = obj[key];
        const newKey = prefix ? `${prefix}.${key}` : key;
        
        if (value !== null && typeof value === 'object' && !Array.isArray(value)) {
            Object.assign(flattened, flattenObject(value, newKey));
        } else {
            flattened[newKey] = value;
        }
    });
    
    return flattened;
}

// Make functions globally available
window.setLoading = setLoading;
window.setSQLLoading = setSQLLoading;
window.showError = showError;
window.hideError = hideError;
window.displayResults = displayResults;
window.displaySQLResults = displaySQLResults;
window.exportQueryData = exportQueryData;
