/* Ultra Modern AI Data Analysis Interface */

/* Modern Design Variables */
:root {
    /* Colors */
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary: #06b6d4;
    --accent: #f59e0b;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Glass effect */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-2xl: 2rem;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Glass morphism background */
.app-container {
    backdrop-filter: blur(20px);
    background: var(--glass-bg);
    min-height: 100vh;
    position: relative;
}

.app-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Loading animations */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Pulse animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Glow animation */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.5); }
    50% { box-shadow: 0 0 40px rgba(99, 102, 241, 0.8); }
}

/* Modern Button System */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-2xl);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-sql-example {
    background: rgba(16, 185, 129, 0.1);
    color: rgb(16, 185, 129);
    border: 1px solid rgba(16, 185, 129, 0.3);
    backdrop-filter: blur(10px);
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-2xl);
    font-family: 'JetBrains Mono', monospace;
}

.btn-sql-example:hover {
    background: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.5);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Icon buttons */
.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-icon:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

/* Glass Morphism Cards */
.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header i {
    margin-right: 0.75rem;
    font-size: 1.75rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Input Fields */
.input-field {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    color: white;
    font-weight: 500;
    transition: var(--transition);
    font-size: 0.875rem;
}

.input-field:focus {
    outline: none;
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-lg);
    background: rgba(255, 255, 255, 0.15);
}

.input-field::placeholder {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
}

.textarea-field {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    color: #10b981;
    font-family: 'JetBrains Mono', monospace;
    font-weight: 500;
    transition: var(--transition);
    resize: none;
    min-height: 120px;
}

.textarea-field:focus {
    outline: none;
    border-color: rgba(16, 185, 129, 0.5);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), var(--shadow-lg);
    background: rgba(0, 0, 0, 0.4);
}

.textarea-field::placeholder {
    color: rgba(16, 185, 129, 0.6);
    font-weight: 400;
}

/* Modern Select */
.select-field {
    width: 100%;
    padding: 1rem 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    color: white;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.select-field:focus {
    outline: none;
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-lg);
}

.select-field option {
    background: rgba(0, 0, 0, 0.9);
    color: white;
}

/* Modern Tab System */
.tab-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-2xl);
    padding: 0.5rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-list {
    display: flex;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-button {
    flex: 1;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-xl);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.tab-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    border-radius: var(--radius-xl);
}

.tab-button.active {
    color: white;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-lg);
}

.tab-button.active::before {
    opacity: 1;
}

.tab-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.tab-content {
    display: none;
    animation: fadeInUp 0.3s ease-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Material Design Tables */
.data-table {
    @apply min-w-full bg-white rounded-xl overflow-hidden;
    box-shadow: var(--md-elevation-1);
}

.data-table th {
    @apply px-6 py-4 text-left text-sm font-semibold text-gray-600 uppercase tracking-wider bg-gray-50;
    border-bottom: 2px solid #e5e7eb;
}

.data-table td {
    @apply px-6 py-4 text-sm text-gray-800 border-b border-gray-100;
    transition: var(--md-transition-fast);
}

.data-table tr:hover {
    @apply bg-primary-50;
}

.data-table tr:nth-child(even) {
    @apply bg-gray-25;
}

/* Material Design Alerts */
.alert-success {
    @apply p-6 bg-success-50 rounded-xl border-l-4 border-success-500;
    box-shadow: var(--md-elevation-1);
}

.alert-success .alert-icon {
    @apply text-success-600;
}

.alert-error {
    @apply bg-error-50 border-2 border-error-200 rounded-xl p-6;
    box-shadow: var(--md-elevation-2);
}

.alert-error .alert-icon {
    @apply text-error-600;
}

.alert-info {
    @apply p-6 bg-primary-50 rounded-xl border-l-4 border-primary-500;
    box-shadow: var(--md-elevation-1);
}

.alert-info .alert-icon {
    @apply text-primary-600;
}

.alert-warning {
    @apply p-6 bg-warning-50 rounded-xl border-l-4 border-warning-500;
    box-shadow: var(--md-elevation-1);
}

.alert-warning .alert-icon {
    @apply text-warning-600;
}

/* Material Design Code Blocks */
.code-block {
    @apply bg-gray-900 text-green-400 p-6 rounded-xl text-sm overflow-x-auto;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    box-shadow: var(--md-elevation-2);
    border: 1px solid rgba(255,255,255,0.1);
}

.code-block code {
    @apply text-green-300;
}

/* Material Design Export Buttons */
.export-btn-csv {
    @apply text-xs bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg font-medium uppercase tracking-wide;
    box-shadow: var(--md-elevation-1);
    transition: var(--md-transition-fast);
}

.export-btn-csv:hover {
    box-shadow: var(--md-elevation-2);
    transform: translateY(-1px);
}

.export-btn-json {
    @apply text-xs bg-success-500 hover:bg-success-600 text-white px-4 py-2 rounded-lg font-medium uppercase tracking-wide;
    box-shadow: var(--md-elevation-1);
    transition: var(--md-transition-fast);
}

.export-btn-json:hover {
    box-shadow: var(--md-elevation-2);
    transform: translateY(-1px);
}

/* Material Design Chips */
.chip {
    @apply inline-flex items-center px-4 py-2 rounded-full text-sm font-medium;
    transition: var(--md-transition-fast);
}

.chip-primary {
    @apply bg-primary-100 text-primary-800 hover:bg-primary-200;
}

.chip-success {
    @apply bg-success-100 text-success-800 hover:bg-success-200;
}

.chip-warning {
    @apply bg-warning-100 text-warning-800 hover:bg-warning-200;
}

.chip-error {
    @apply bg-error-100 text-error-800 hover:bg-error-200;
}

/* Material Design Floating Action Button */
.fab {
    @apply fixed bottom-6 right-6 w-14 h-14 bg-primary-600 text-white rounded-full flex items-center justify-center;
    box-shadow: var(--md-elevation-3);
    transition: var(--md-transition);
    z-index: 1000;
}

.fab:hover {
    @apply bg-primary-700;
    box-shadow: var(--md-elevation-4);
    transform: scale(1.1);
}

/* Material Design Progress Indicators */
.progress-linear {
    @apply w-full h-1 bg-gray-200 rounded-full overflow-hidden;
}

.progress-linear-bar {
    @apply h-full bg-primary-500 rounded-full;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        @apply px-4;
    }

    .card {
        @apply p-6 mb-6;
    }

    .card-header {
        @apply text-xl;
    }

    .flex-responsive {
        @apply flex-col space-y-4;
    }

    .flex-responsive > * {
        @apply w-full;
    }

    .btn-primary, .btn-success {
        @apply w-full justify-center;
    }
}

/* Material Design Animation classes */
.fade-in {
    animation: materialFadeIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes materialFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.slide-up {
    animation: materialSlideUp 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes materialSlideUp {
    from {
        transform: translateY(40px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.scale-in {
    animation: materialScaleIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes materialScaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Material Design Utility classes */
.text-truncate {
    @apply truncate;
}

.scrollable {
    @apply overflow-auto max-h-96;
}

.border-left-accent {
    @apply border-l-4 border-primary-500;
}

.elevation-1 { box-shadow: var(--md-elevation-1); }
.elevation-2 { box-shadow: var(--md-elevation-2); }
.elevation-3 { box-shadow: var(--md-elevation-3); }
.elevation-4 { box-shadow: var(--md-elevation-4); }

/* Material Design Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary-300 rounded-full;
    transition: var(--md-transition-fast);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
}

/* Material Design Ripple Effect */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* Material Design Loading States */
.skeleton {
    @apply bg-gray-200 rounded animate-pulse;
}

.skeleton-text {
    @apply h-4 bg-gray-200 rounded animate-pulse;
}

.skeleton-avatar {
    @apply w-12 h-12 bg-gray-200 rounded-full animate-pulse;
}

/* Material Design Status Indicators */
.status-online {
    @apply w-3 h-3 bg-success-500 rounded-full;
}

.status-offline {
    @apply w-3 h-3 bg-gray-400 rounded-full;
}

.status-busy {
    @apply w-3 h-3 bg-error-500 rounded-full;
}

/* Material Design Dividers */
.divider {
    @apply border-t border-gray-200 my-6;
}

.divider-vertical {
    @apply border-l border-gray-200 mx-6;
}

/* Material Design Typography */
.headline-1 { @apply text-6xl font-light tracking-tight; }
.headline-2 { @apply text-4xl font-light tracking-tight; }
.headline-3 { @apply text-3xl font-normal; }
.headline-4 { @apply text-2xl font-normal; }
.headline-5 { @apply text-xl font-normal; }
.headline-6 { @apply text-lg font-medium; }

.subtitle-1 { @apply text-base font-normal; }
.subtitle-2 { @apply text-sm font-medium; }

.body-1 { @apply text-base font-normal; }
.body-2 { @apply text-sm font-normal; }

.caption { @apply text-xs font-normal; }
.overline { @apply text-xs font-medium uppercase tracking-wider; }

/* Task Management Styles */
#task-panel {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

#task-indicator {
    animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.task-progress-bar {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
    0% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    100% {
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
    }
}

/* Quick Actions Menu */
#quick-actions-menu {
    animation: fadeInUp 0.3s ease-out;
}

#quick-actions-menu button {
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#quick-actions-menu button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Task Status Indicators */
.task-status-pending {
    color: #f59e0b;
    animation: pulse 2s infinite;
}

.task-status-running {
    color: #3b82f6;
    animation: spin 2s linear infinite;
}

.task-status-completed {
    color: #10b981;
}

.task-status-failed {
    color: #ef4444;
    animation: shake 0.5s ease-in-out;
}

.task-status-cancelled {
    color: #6b7280;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Task Panel Scrollbar */
#task-list::-webkit-scrollbar {
    width: 4px;
}

#task-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

#task-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

#task-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Analytics Tab Styles */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.analytics-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
}

.analytics-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.task-progress-bar {
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    height: 8px;
    border-radius: 4px;
    transition: width 0.5s ease;
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}
