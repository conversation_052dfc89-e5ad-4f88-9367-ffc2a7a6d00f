"""
AutoGen agents for ClickHouse database interaction and analysis.
Updated for AutoGen 0.6+ (new API)
"""

import asyncio
from typing import Dict, Any, List, Optional
import pandas as pd
import json
from clickhouse_client import clickhouse_client
from config import LLM_CONFIG, LLM_PROVIDER, AGENT_CONFIG
import logging

# New AutoGen imports
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient

logger = logging.getLogger(__name__)

def get_model_client():
    """Get the appropriate model client based on configuration."""
    if LLM_PROVIDER == "openai":
        return OpenAIChatCompletionClient(
            model=LLM_CONFIG["model"],
            api_key=LLM_CONFIG["api_key"],
            base_url=LLM_CONFIG.get("base_url"),
            temperature=LLM_CONFIG["temperature"]
        )
    else:
        raise ValueError(f"Unsupported LLM provider: {LLM_PROVIDER}")

def execute_clickhouse_query(query: str) -> str:
    """
    Execute a ClickHouse query and return results as formatted string.
    
    Args:
        query: SQL query to execute
        
    Returns:
        Formatted query results or error message
    """
    try:
        # Validate query (basic security check)
        query_lower = query.lower().strip()
        if not query_lower.startswith('select'):
            return "Error: Only SELECT queries are allowed for security reasons."
        
        # Execute query
        result_df = clickhouse_client.execute_query(query)
        
        if result_df.empty:
            return "Query executed successfully but returned no results."
        
        # Format results
        if len(result_df) > 20:
            formatted_result = f"Query returned {len(result_df)} rows. Showing first 20:\n\n"
            formatted_result += result_df.head(20).to_string(index=False)
            formatted_result += f"\n\n... and {len(result_df) - 20} more rows."
        else:
            formatted_result = f"Query returned {len(result_df)} rows:\n\n"
            formatted_result += result_df.to_string(index=False)
        
        return formatted_result
        
    except Exception as e:
        error_msg = f"Query execution failed: {str(e)}"
        logger.error(error_msg)
        return error_msg

def get_database_schema() -> str:
    """Get database schema information."""
    try:
        tables = clickhouse_client.get_tables()
        schema_info = "Database Schema:\n\n"
        
        for table in tables:
            schema_info += f"Table: {table}\n"
            schema = clickhouse_client.get_table_schema(table)
            for column in schema:
                schema_info += f"  - {column['name']}: {column['type']}\n"
            schema_info += "\n"
        
        return schema_info
    except Exception as e:
        return f"Error retrieving schema: {str(e)}"

def get_table_sample(table_name: str, limit: int = 5) -> str:
    """Get sample data from a table."""
    try:
        sample_df = clickhouse_client.get_table_sample(table_name, limit)
        return f"Sample data from {table_name}:\n\n{sample_df.to_string(index=False)}"
    except Exception as e:
        return f"Error retrieving sample data: {str(e)}"

# Create specialized agents
def create_data_agent() -> AssistantAgent:
    """Create a data agent specialized in database queries."""
    system_message = """
    You are a Data Agent specialized in ClickHouse database operations. Your responsibilities:
    
    1. Execute SQL queries on ClickHouse database
    2. Retrieve and format data for analysis
    3. Provide database schema information
    4. Suggest optimized queries for better performance
    
    Available functions:
    - execute_clickhouse_query: Execute SELECT queries
    - get_database_schema: Get table schemas
    - get_table_sample: Get sample data from tables
    
    Always validate queries before execution and provide clear, formatted results.
    When writing queries, consider ClickHouse-specific optimizations like:
    - Using appropriate ORDER BY clauses
    - Leveraging ClickHouse functions for aggregations
    - Using LIMIT for large result sets
    
    When a user asks for data, first check the schema if needed, then write and execute appropriate queries.
    """
    
    model_client = get_model_client()
    
    return AssistantAgent(
        name="DataAgent",
        model_client=model_client,
        system_message=system_message
    )

def create_analyst_agent() -> AssistantAgent:
    """Create an analyst agent for data analysis and insights."""
    system_message = """
    You are an Analyst Agent specialized in data analysis and business insights. Your responsibilities:
    
    1. Analyze data retrieved from ClickHouse database
    2. Generate business insights and recommendations
    3. Create summaries and reports
    4. Identify trends and patterns in data
    
    When analyzing data:
    - Look for trends, patterns, and anomalies
    - Provide actionable business insights
    - Suggest follow-up questions or analyses
    - Present findings in a clear, business-friendly format
    
    Work with the Data Agent to get the data you need for analysis.
    Focus on providing valuable insights rather than just describing the data.
    """
    
    model_client = get_model_client()
    
    return AssistantAgent(
        name="AnalystAgent",
        model_client=model_client,
        system_message=system_message
    )

# Agent factory
class AgentFactory:
    """Factory class for creating and managing agents."""
    
    @staticmethod
    def create_agents() -> Dict[str, AssistantAgent]:
        """Create all agents and return as dictionary."""
        return {
            "data_agent": create_data_agent(),
            "analyst_agent": create_analyst_agent()
        }
    
    @staticmethod
    def create_team(agents: Dict[str, AssistantAgent]) -> RoundRobinGroupChat:
        """Create a team with all agents."""
        agent_list = list(agents.values())
        
        return RoundRobinGroupChat(
            participants=agent_list
        )

# Async wrapper functions for the new API
async def run_simple_query_demo():
    """Run a simple query demonstration."""
    print("🔍 Running Simple Query Demo")
    print("-" * 40)
    
    # Create data agent
    data_agent = create_data_agent()
    
    # Test queries
    queries = [
        "Show me the database schema",
        "What are the total sales by region?",
        "Show me the top 5 products by sales amount"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        try:
            response = await data_agent.run(task=query)
            print(f"Response: {response.messages[-1].content}")
        except Exception as e:
            print(f"Error: {e}")
        print("-" * 40)

async def run_analysis_demo():
    """Run an analysis demonstration with multiple agents."""
    print("🤖 Running Multi-Agent Analysis Demo")
    print("-" * 40)
    
    # Create agents
    agents = AgentFactory.create_agents()
    team = AgentFactory.create_team(agents)
    
    # Analysis task
    task = """
    Please analyze the sales data in our ClickHouse database:
    1. Get an overview of the data structure
    2. Analyze sales performance by region
    3. Identify top-performing products
    4. Provide business insights and recommendations
    """
    
    try:
        response = await team.run(task=task)
        print("Analysis Results:")
        for message in response.messages:
            print(f"{message.source}: {message.content}")
            print("-" * 40)
    except Exception as e:
        print(f"Error: {e}")

# Synchronous wrapper functions for backward compatibility
def run_sync_demo():
    """Run demo in synchronous mode."""
    try:
        asyncio.run(run_simple_query_demo())
    except Exception as e:
        print(f"Demo failed: {e}")

def run_sync_analysis():
    """Run analysis in synchronous mode."""
    try:
        asyncio.run(run_analysis_demo())
    except Exception as e:
        print(f"Analysis failed: {e}")

# Tool registration for the new API
def register_tools():
    """Register tools with agents (placeholder for new API)."""
    # In the new AutoGen API, tools are registered differently
    # This is a placeholder for future implementation
    pass
