#!/usr/bin/env python3
"""
图表生成模块 - 根据数据自动生成可视化图表
"""

import pandas as pd
import json
import os
from typing import Dict, Any, List, Optional, Tuple
import logging
import re

logger = logging.getLogger(__name__)

# Try to import visualization libraries, but don't fail if they're not available
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    # 设置seaborn样式
    sns.set_style("whitegrid")
    sns.set_palette("husl")
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger.warning("Matplotlib/Seaborn not available, using simplified chart generation")

try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.utils import PlotlyJSONEncoder
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logger.warning("Plotly not available, using simplified chart generation")

class ChartGenerator:
    """图表生成器类"""
    
    def __init__(self):
        self.chart_dir = "static/charts"
        os.makedirs(self.chart_dir, exist_ok=True)
    
    def analyze_data_type(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析数据类型，确定最适合的图表类型"""
        if df.empty:
            return {"chart_type": "none", "reason": "数据为空"}
        
        num_cols = len(df.columns)
        num_rows = len(df)
        
        # 分析列的数据类型
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime']).columns.tolist()
        
        analysis = {
            "num_columns": num_cols,
            "num_rows": num_rows,
            "numeric_columns": numeric_cols,
            "categorical_columns": categorical_cols,
            "datetime_columns": datetime_cols
        }
        
        # 根据数据特征推荐图表类型
        if num_cols == 2:
            if len(numeric_cols) == 2:
                analysis["chart_type"] = "scatter"
                analysis["reason"] = "两个数值列适合散点图"
            elif len(numeric_cols) == 1 and len(categorical_cols) == 1:
                if num_rows <= 20:
                    analysis["chart_type"] = "bar"
                    analysis["reason"] = "分类数据适合柱状图"
                else:
                    analysis["chart_type"] = "bar"
                    analysis["reason"] = "数据较多，使用柱状图"
            elif len(datetime_cols) == 1 and len(numeric_cols) == 1:
                analysis["chart_type"] = "line"
                analysis["reason"] = "时间序列数据适合折线图"
        elif num_cols == 1:
            if len(numeric_cols) == 1:
                analysis["chart_type"] = "histogram"
                analysis["reason"] = "单个数值列适合直方图"
            else:
                analysis["chart_type"] = "pie"
                analysis["reason"] = "单个分类列适合饼图"
        elif num_cols > 2:
            if len(numeric_cols) >= 2:
                analysis["chart_type"] = "heatmap"
                analysis["reason"] = "多个数值列适合热力图"
            else:
                analysis["chart_type"] = "bar"
                analysis["reason"] = "多列数据使用柱状图"
        else:
            analysis["chart_type"] = "table"
            analysis["reason"] = "数据结构复杂，使用表格显示"
        
        return analysis
    
    def generate_matplotlib_chart(self, df: pd.DataFrame, chart_type: str, title: str = "数据分析图表") -> str:
        """使用matplotlib生成图表并返回base64编码的图片"""
        try:
            plt.figure(figsize=(10, 6))
            
            if chart_type == "bar":
                if len(df.columns) == 2:
                    x_col, y_col = df.columns[0], df.columns[1]
                    plt.bar(df[x_col], df[y_col])
                    plt.xlabel(x_col)
                    plt.ylabel(y_col)
                    plt.xticks(rotation=45)
            
            elif chart_type == "line":
                if len(df.columns) >= 2:
                    x_col, y_col = df.columns[0], df.columns[1]
                    plt.plot(df[x_col], df[y_col], marker='o')
                    plt.xlabel(x_col)
                    plt.ylabel(y_col)
                    plt.xticks(rotation=45)
            
            elif chart_type == "scatter":
                if len(df.columns) >= 2:
                    x_col, y_col = df.columns[0], df.columns[1]
                    plt.scatter(df[x_col], df[y_col], alpha=0.6)
                    plt.xlabel(x_col)
                    plt.ylabel(y_col)
            
            elif chart_type == "pie":
                if len(df.columns) >= 2:
                    labels_col, values_col = df.columns[0], df.columns[1]
                    plt.pie(df[values_col], labels=df[labels_col], autopct='%1.1f%%')
                elif len(df.columns) == 1:
                    # 如果只有一列，统计值的频率
                    value_counts = df.iloc[:, 0].value_counts()
                    plt.pie(value_counts.values, labels=value_counts.index, autopct='%1.1f%%')
            
            elif chart_type == "histogram":
                if len(df.columns) >= 1:
                    numeric_col = df.select_dtypes(include=['number']).columns[0]
                    plt.hist(df[numeric_col], bins=20, alpha=0.7)
                    plt.xlabel(numeric_col)
                    plt.ylabel('频率')
            
            elif chart_type == "heatmap":
                numeric_df = df.select_dtypes(include=['number'])
                if not numeric_df.empty:
                    sns.heatmap(numeric_df.corr(), annot=True, cmap='coolwarm', center=0)
            
            plt.title(title)
            plt.tight_layout()
            
            # 保存为base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            logger.error(f"生成matplotlib图表失败: {e}")
            return None
    
    def generate_plotly_chart(self, df: pd.DataFrame, chart_type: str, title: str = "数据分析图表") -> Dict[str, Any]:
        """使用plotly生成交互式图表"""
        try:
            fig = None
            
            if chart_type == "bar":
                if len(df.columns) >= 2:
                    x_col, y_col = df.columns[0], df.columns[1]
                    fig = px.bar(df, x=x_col, y=y_col, title=title)
            
            elif chart_type == "line":
                if len(df.columns) >= 2:
                    x_col, y_col = df.columns[0], df.columns[1]
                    fig = px.line(df, x=x_col, y=y_col, title=title, markers=True)
            
            elif chart_type == "scatter":
                if len(df.columns) >= 2:
                    x_col, y_col = df.columns[0], df.columns[1]
                    fig = px.scatter(df, x=x_col, y=y_col, title=title)
            
            elif chart_type == "pie":
                if len(df.columns) >= 2:
                    labels_col, values_col = df.columns[0], df.columns[1]
                    fig = px.pie(df, names=labels_col, values=values_col, title=title)
                elif len(df.columns) == 1:
                    value_counts = df.iloc[:, 0].value_counts()
                    fig = px.pie(names=value_counts.index, values=value_counts.values, title=title)
            
            elif chart_type == "histogram":
                if len(df.columns) >= 1:
                    numeric_col = df.select_dtypes(include=['number']).columns[0]
                    fig = px.histogram(df, x=numeric_col, title=title)
            
            elif chart_type == "heatmap":
                numeric_df = df.select_dtypes(include=['number'])
                if not numeric_df.empty:
                    corr_matrix = numeric_df.corr()
                    fig = px.imshow(corr_matrix, text_auto=True, aspect="auto", title=title)
            
            if fig:
                fig.update_layout(
                    font=dict(size=12),
                    title_font_size=16,
                    showlegend=True
                )
                return json.loads(fig.to_json())
            
            return None
            
        except Exception as e:
            logger.error(f"生成plotly图表失败: {e}")
            return None
    
    def generate_chart_from_query_result(self, query_result: str, query: str = "") -> Dict[str, Any]:
        """根据查询结果自动生成图表"""
        try:
            # 解析查询结果为DataFrame
            lines = query_result.strip().split('\n')
            if len(lines) < 3:
                return {"error": "查询结果数据不足"}
            
            # 找到表格数据的开始位置
            data_start = -1
            for i, line in enumerate(lines):
                if any(char in line for char in ['|', '\t']) or (i > 0 and line.strip() and not line.startswith('Query')):
                    data_start = i
                    break
            
            if data_start == -1:
                return {"error": "无法解析查询结果"}
            
            # 解析表格数据
            header_line = lines[data_start].strip()
            data_lines = [line.strip() for line in lines[data_start + 1:] if line.strip() and not line.startswith('...')]
            
            # 分割列
            if '\t' in header_line:
                delimiter = '\t'
            else:
                delimiter = None  # 使用空格分割
            
            if delimiter:
                headers = [h.strip() for h in header_line.split(delimiter)]
                data_rows = []
                for line in data_lines:
                    row = [cell.strip() for cell in line.split(delimiter)]
                    if len(row) == len(headers):
                        data_rows.append(row)
            else:
                # 使用空格分割，更复杂的解析
                headers = header_line.split()
                data_rows = []
                for line in data_lines:
                    row = line.split()
                    if len(row) >= len(headers):
                        data_rows.append(row[:len(headers)])
            
            if not data_rows:
                return {"error": "无法解析数据行"}
            
            # 创建DataFrame
            df = pd.DataFrame(data_rows, columns=headers)
            
            # 尝试转换数值列
            for col in df.columns:
                try:
                    df[col] = pd.to_numeric(df[col])
                except:
                    pass  # 保持为字符串
            
            # 分析数据并生成图表
            analysis = self.analyze_data_type(df)
            chart_type = analysis.get("chart_type", "bar")
            
            # 生成图表标题
            title = f"数据分析结果 - {chart_type.upper()}"
            if query:
                title = f"查询结果可视化"
            
            # 生成matplotlib图表
            matplotlib_chart = self.generate_matplotlib_chart(df, chart_type, title)
            
            # 生成plotly图表
            plotly_chart = self.generate_plotly_chart(df, chart_type, title)
            
            return {
                "success": True,
                "chart_type": chart_type,
                "analysis": analysis,
                "matplotlib_chart": matplotlib_chart,
                "plotly_chart": plotly_chart,
                "data_summary": {
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": df.columns.tolist()
                }
            }
            
        except Exception as e:
            logger.error(f"生成图表失败: {e}")
            return {"error": f"生成图表失败: {str(e)}"}

# 全局图表生成器实例
chart_generator = ChartGenerator()
