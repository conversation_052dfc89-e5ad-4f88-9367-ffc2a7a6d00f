#!/usr/bin/env python3
"""
Main application for AutoGen + ClickHouse demo.
"""

import logging
import sys
from typing import Dict, Any
from agents import AgentFactory, execute_clickhouse_query, get_database_schema, get_table_sample
from clickhouse_client import clickhouse_client
import autogen

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def verify_setup():
    """Verify that the database setup is complete."""
    try:
        tables = clickhouse_client.get_tables()
        if not tables:
            logger.error("No tables found in database. Please run setup_database.py first.")
            return False
        
        logger.info(f"Found tables: {tables}")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

def demo_basic_queries():
    """Demonstrate basic database queries."""
    print("\n" + "="*60)
    print("BASIC QUERY DEMONSTRATION")
    print("="*60)
    
    # Show schema
    print("\n1. Database Schema:")
    print("-" * 30)
    schema = get_database_schema()
    print(schema)
    
    # Show sample data
    print("\n2. Sample Data:")
    print("-" * 30)
    tables = clickhouse_client.get_tables()
    for table in tables:
        print(f"\n{table.upper()}:")
        sample = get_table_sample(table, 3)
        print(sample)
    
    # Execute some example queries
    print("\n3. Example Queries:")
    print("-" * 30)
    
    queries = [
        ("Total sales by region", 
         "SELECT region, SUM(total_amount) as total_sales FROM sales GROUP BY region ORDER BY total_sales DESC"),
        
        ("Top 5 products by sales", 
         "SELECT p.name, SUM(s.total_amount) as total_sales FROM sales s JOIN products p ON s.product_id = p.id GROUP BY p.name ORDER BY total_sales DESC LIMIT 5"),
        
        ("Monthly sales trend", 
         "SELECT toYYYYMM(sale_date) as month, SUM(total_amount) as monthly_sales FROM sales GROUP BY month ORDER BY month"),
        
        ("Customer demographics", 
         "SELECT country, COUNT(*) as customer_count, AVG(age) as avg_age FROM customers GROUP BY country ORDER BY customer_count DESC")
    ]
    
    for description, query in queries:
        print(f"\n{description}:")
        print(f"Query: {query}")
        result = execute_clickhouse_query(query)
        print(f"Result:\n{result}")
        print("-" * 50)

def run_interactive_mode():
    """Run the interactive agent system."""
    print("\n" + "="*60)
    print("INTERACTIVE AGENT MODE")
    print("="*60)
    print("Starting AutoGen agents for interactive data analysis...")
    print("Type 'TERMINATE' to end the conversation.")
    print("-" * 60)
    
    try:
        # Create agents
        agents = AgentFactory.create_agents()
        
        # Create group chat
        group_chat = AgentFactory.create_group_chat(agents)
        manager = AgentFactory.create_group_chat_manager(group_chat)
        
        # Start conversation
        initial_message = """
        Welcome to the ClickHouse Data Analysis System!
        
        I can help you analyze data in our ClickHouse database. Here's what I can do:
        
        1. Execute SQL queries on the database
        2. Provide schema information
        3. Generate data insights and analysis
        4. Create reports and summaries
        
        Available tables:
        - sales: Sales transaction data
        - products: Product information
        - customers: Customer demographics
        
        What would you like to analyze today?
        """
        
        agents["user_proxy"].initiate_chat(
            manager,
            message=initial_message
        )
        
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        logger.error(f"Error in interactive mode: {e}")
        raise

def run_automated_demo():
    """Run automated demo with predefined scenarios."""
    print("\n" + "="*60)
    print("AUTOMATED DEMO MODE")
    print("="*60)
    
    # Create agents with automated mode
    agents = AgentFactory.create_agents()
    
    # Override user proxy for automated mode
    agents["user_proxy"] = autogen.UserProxyAgent(
        name="UserProxy",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=10,
        is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
        code_execution_config=False,
    )
    
    # Create group chat
    group_chat = AgentFactory.create_group_chat(agents)
    manager = AgentFactory.create_group_chat_manager(group_chat)
    
    # Demo scenarios
    scenarios = [
        "Analyze the top-performing products and provide business insights.",
        "Show me the sales trends by region and suggest areas for improvement.",
        "Analyze customer demographics and identify target segments.",
        "Generate a comprehensive sales performance report."
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n--- Scenario {i}: {scenario} ---")
        try:
            agents["user_proxy"].initiate_chat(
                manager,
                message=scenario + " Please provide analysis and then TERMINATE."
            )
        except Exception as e:
            logger.error(f"Error in scenario {i}: {e}")
        print("\n" + "="*60)

def main():
    """Main application entry point."""
    print("AutoGen + ClickHouse Demo")
    print("=" * 40)
    
    # Verify setup
    if not verify_setup():
        print("Please run 'python setup_database.py' first to initialize the database.")
        sys.exit(1)
    
    # Show menu
    while True:
        print("\nSelect demo mode:")
        print("1. Basic Query Demonstration")
        print("2. Interactive Agent Mode")
        print("3. Automated Demo")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            demo_basic_queries()
        elif choice == "2":
            run_interactive_mode()
        elif choice == "3":
            run_automated_demo()
        elif choice == "4":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)
    finally:
        # Clean up
        try:
            clickhouse_client.close()
        except:
            pass
