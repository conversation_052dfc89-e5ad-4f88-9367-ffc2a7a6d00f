// NEW Utils file to bypass cache - Version 2025-08-01-14:25
console.log('🚀 NEW Utils.js loaded successfully!');

// Simple table generator that WILL work
function createDataTableNew(data) {
    console.log('🔥 NEW createDataTableNew called with:', data);
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        console.log('❌ No valid data');
        return '<div class="bg-red-100 p-4 rounded"><p class="text-red-600">No data to display</p></div>';
    }
    
    const firstRow = data[0];
    if (!firstRow || typeof firstRow !== 'object') {
        console.log('❌ Invalid first row');
        return '<div class="bg-red-100 p-4 rounded"><p class="text-red-600">Invalid data format</p></div>';
    }
    
    const columns = Object.keys(firstRow);
    console.log('🔥 Columns found:', columns);
    
    let html = `
        <div class="bg-white border rounded-lg overflow-hidden shadow">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        ${columns.map(col => `<th class="px-4 py-2 text-left font-medium text-gray-700 border-b">${col}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${data.slice(0, 10).map((row, index) => `
                        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}">
                            ${columns.map(col => `<td class="px-4 py-2 border-b text-gray-900">${row[col] || ''}</td>`).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            ${data.length > 10 ? `<div class="px-4 py-2 bg-gray-50 text-sm text-gray-600">Showing 10 of ${data.length} rows</div>` : ''}
        </div>
    `;
    
    console.log('🔥 NEW table HTML generated successfully!');
    return html;
}

// Override the displayResults function
function displayResultsNew(results) {
    console.log('🔥 NEW displayResults called with:', results);

    if (!results || results.length === 0) {
        document.getElementById('no-results').style.display = 'block';
        document.getElementById('results-container').style.display = 'none';
        return;
    }

    document.getElementById('no-results').style.display = 'none';
    document.getElementById('results-container').style.display = 'block';

    const resultsContainer = document.getElementById('results-list');
    resultsContainer.innerHTML = '';

    results.forEach((result, index) => {
        console.log(`🔥 Processing result ${index}:`, result);

        // Check if this is a complex task decomposition result
        if (result.task_decomposition_used && result.sub_tasks) {
            createComplexTaskCard(result, index, resultsContainer);
        } else {
            createSimpleTaskCard(result, index, resultsContainer);
        }
    });

    console.log('🔥 NEW displayResults completed successfully!');
}

// Create card for complex task decomposition results
function createComplexTaskCard(result, index, container) {
    const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';

    const resultCard = document.createElement('div');
    resultCard.className = 'bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200';

    // Parse the complex answer to extract sub-tasks
    const subTasks = parseComplexTaskAnswer(result.answer);

    resultCard.innerHTML = `
        <div class="flex justify-between items-start mb-4">
            <div class="flex items-center">
                <i class="material-icons text-purple-600 mr-2">account_tree</i>
                <h3 class="text-lg font-medium text-gray-800">Complex Task Analysis</h3>
                <span class="ml-2 bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm">${result.sub_tasks.length} sub-tasks</span>
            </div>
            <span class="text-gray-500 text-sm">${timestamp}</span>
        </div>

        <div class="bg-blue-50 p-4 rounded-lg mb-6">
            <div class="flex items-center mb-2">
                <i class="material-icons text-blue-600 mr-2">help_outline</i>
                <h4 class="font-medium text-gray-800">Original Question</h4>
            </div>
            <p class="text-gray-700">${result.question}</p>
        </div>

        <div class="mb-6">
            <div class="flex items-center mb-4">
                <i class="material-icons text-green-600 mr-2">list_alt</i>
                <h4 class="font-medium text-gray-800">Task Breakdown</h4>
            </div>
            <div class="space-y-3">
                ${result.sub_tasks.map((task, i) => `
                    <div class="bg-gray-50 p-3 rounded-lg border-l-4 border-blue-500">
                        <div class="flex items-center mb-1">
                            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-2">${i + 1}</span>
                            <span class="font-medium text-gray-800">Sub-task ${i + 1}</span>
                        </div>
                        <p class="text-gray-700 text-sm ml-8">${task}</p>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="mb-6">
            <div class="flex items-center mb-4">
                <i class="material-icons text-orange-600 mr-2">analytics</i>
                <h4 class="font-medium text-gray-800">Analysis Results</h4>
                <button onclick="toggleComplexResults(${index})" class="ml-2 text-blue-600 hover:text-blue-800 text-sm">
                    <i class="material-icons text-sm">expand_more</i> Show Details
                </button>
            </div>
            <div id="complex-results-${index}" class="hidden">
                <div class="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                    ${formatComplexTaskResults(subTasks)}
                </div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center mb-2">
                    <i class="material-icons text-green-600 mr-2">check_circle</i>
                    <span class="font-medium text-green-800">Analysis Complete</span>
                </div>
                <p class="text-green-700 text-sm">Successfully executed ${result.sub_tasks.length} sub-tasks with comprehensive data analysis.</p>
            </div>
        </div>

        <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center text-gray-500">
                <i class="material-icons text-sm mr-1">timer</i>
                <span class="text-sm">Execution time: ${(result.execution_time || 0).toFixed(2)}s</span>
            </div>
            <div class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
                <i class="material-icons text-sm mr-1">account_tree</i>
                Complex Analysis
            </div>
        </div>
    `;

    container.appendChild(resultCard);
}

// Create card for simple task results
function createSimpleTaskCard(result, index, container) {
    const timestamp = result.timestamp ? new Date(result.timestamp).toLocaleString() : 'Just now';
    const formattedAnswer = result.answer || 'No answer received';

    let dataSection = '';
    if (result.data && result.data.length > 0) {
        console.log('🔥 Creating table for result data:', result.data);
        const tableHtml = createDataTableNew(result.data);

        dataSection = `
            <div class="mt-6">
                <div class="flex justify-between items-center mb-4">
                    <div class="flex items-center">
                        <i class="material-icons text-blue-600 mr-2">table_view</i>
                        <h4 class="text-lg font-medium text-gray-800">Query Results</h4>
                        <span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">${result.data.length} rows</span>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="downloadData(${index}, 'csv')" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">
                            CSV
                        </button>
                        <button onclick="downloadData(${index}, 'json')" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                            JSON
                        </button>
                    </div>
                </div>
                ${tableHtml}
            </div>
        `;
    }

    const resultCard = document.createElement('div');
    resultCard.className = 'bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200';
    resultCard.innerHTML = `
        <div class="flex justify-between items-start mb-4">
            <div class="flex items-center">
                <i class="material-icons text-blue-600 mr-2">help_outline</i>
                <h3 class="text-lg font-medium text-gray-800">Question</h3>
            </div>
            <span class="text-gray-500 text-sm">${timestamp}</span>
        </div>

        <div class="bg-blue-50 p-4 rounded-lg mb-6">
            <p class="text-gray-700">${result.question}</p>
        </div>

        <div class="flex items-center mb-4">
            <i class="material-icons text-green-600 mr-2">psychology</i>
            <h3 class="text-lg font-medium text-gray-800">AI Answer</h3>
        </div>

        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <p class="text-gray-700">${formattedAnswer}</p>
        </div>

        ${dataSection}

        <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center text-gray-500">
                <i class="material-icons text-sm mr-1">timer</i>
                <span class="text-sm">Execution time: ${(result.execution_time || 0).toFixed(2)}s</span>
            </div>
            <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                <i class="material-icons text-sm mr-1">check_circle</i>
                Complete
            </div>
        </div>
    `;

    container.appendChild(resultCard);
}

// Parse complex task answer to extract sub-task results
function parseComplexTaskAnswer(answer) {
    const subTasks = [];

    // Split by sub-task markers
    const sections = answer.split(/\*\*子任务 \d+\*\*:/);

    for (let i = 1; i < sections.length; i++) {
        const section = sections[i].trim();
        const nextTaskIndex = section.indexOf('==================================================');
        const taskContent = nextTaskIndex > 0 ? section.substring(0, nextTaskIndex).trim() : section.trim();

        subTasks.push({
            index: i,
            content: taskContent,
            hasSQL: taskContent.includes('🔍 **执行的SQL查询:**'),
            hasResults: taskContent.includes('📊 **查询结果:**')
        });
    }

    return subTasks;
}

// Format complex task results for display
function formatComplexTaskResults(subTasks) {
    return subTasks.map(task => `
        <div class="mb-6 p-4 bg-white rounded-lg border border-gray-200">
            <div class="flex items-center mb-3">
                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium mr-3">${task.index}</span>
                <h5 class="font-medium text-gray-800">Sub-task ${task.index} Results</h5>
                ${task.hasSQL ? '<span class="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">SQL Executed</span>' : ''}
                ${task.hasResults ? '<span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Data Returned</span>' : ''}
            </div>
            <div class="text-sm text-gray-700 whitespace-pre-wrap">${task.content}</div>
        </div>
    `).join('');
}

// Toggle complex results visibility
function toggleComplexResults(index) {
    const resultsDiv = document.getElementById(`complex-results-${index}`);
    const button = event.target.closest('button');
    const icon = button.querySelector('i');

    if (resultsDiv.classList.contains('hidden')) {
        resultsDiv.classList.remove('hidden');
        icon.textContent = 'expand_less';
        button.innerHTML = '<i class="material-icons text-sm">expand_less</i> Hide Details';
    } else {
        resultsDiv.classList.add('hidden');
        icon.textContent = 'expand_more';
        button.innerHTML = '<i class="material-icons text-sm">expand_more</i> Show Details';
    }
}

// Simple download function
function downloadData(index, format) {
    console.log('🔥 Download requested:', index, format);
    alert(`Download ${format} for result ${index} - Feature coming soon!`);
}

console.log('🔥 NEW Utils.js fully loaded and ready!');
