#!/usr/bin/env python3
"""
Ollama setup and configuration script for AutoGen + ClickHouse demo.
"""

import subprocess
import sys
import time
import requests
import json
from pathlib import Path

def check_ollama_installed():
    """Check if Ollama is installed."""
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Ollama is installed: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Ollama is not installed")
        return False

def check_ollama_running():
    """Check if Ollama server is running."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama server is running")
            return True
        else:
            print("❌ Ollama server is not responding correctly")
            return False
    except requests.exceptions.RequestException:
        print("❌ Ollama server is not running")
        return False

def start_ollama_server():
    """Start Ollama server."""
    print("🚀 Starting Ollama server...")
    try:
        # Start Ollama in background
        subprocess.Popen(['ollama', 'serve'], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # Wait for server to start
        for i in range(30):  # Wait up to 30 seconds
            time.sleep(1)
            if check_ollama_running():
                return True
            print(f"⏳ Waiting for Ollama server... ({i+1}/30)")
        
        print("❌ Failed to start Ollama server")
        return False
    except Exception as e:
        print(f"❌ Error starting Ollama server: {e}")
        return False

def list_available_models():
    """List available Ollama models."""
    try:
        response = requests.get("http://localhost:11434/api/tags")
        if response.status_code == 200:
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            return models
        return []
    except Exception:
        return []

def pull_model(model_name):
    """Pull a model from Ollama."""
    print(f"📥 Pulling model: {model_name}")
    try:
        # Use streaming API to show progress
        response = requests.post(
            "http://localhost:11434/api/pull",
            json={"name": model_name},
            stream=True
        )
        
        if response.status_code == 200:
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line)
                        if 'status' in data:
                            print(f"📥 {data['status']}")
                        if data.get('status') == 'success':
                            print(f"✅ Model {model_name} pulled successfully")
                            return True
                    except json.JSONDecodeError:
                        continue
        else:
            print(f"❌ Failed to pull model: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error pulling model: {e}")
        return False

def recommend_models():
    """Recommend models for different use cases."""
    recommendations = {
        "Small/Fast": ["llama2:7b", "mistral:7b", "neural-chat:7b"],
        "Medium": ["llama2:13b", "codellama:13b", "mistral:7b-instruct"],
        "Large/Best": ["llama2:70b", "codellama:34b", "mixtral:8x7b"],
        "Code-focused": ["codellama:7b", "codellama:13b", "deepseek-coder:6.7b"],
        "Chat-optimized": ["llama2:7b-chat", "mistral:7b-instruct", "neural-chat:7b"]
    }
    
    print("\n📋 Recommended models:")
    for category, models in recommendations.items():
        print(f"\n{category}:")
        for model in models:
            print(f"  - {model}")

def setup_ollama_env():
    """Set up environment for Ollama."""
    env_file = Path('.env')
    
    if env_file.exists():
        # Read existing .env
        with open(env_file, 'r') as f:
            content = f.read()
        
        # Update LLM_PROVIDER if it exists
        if 'LLM_PROVIDER=' in content:
            content = content.replace('LLM_PROVIDER=openai', 'LLM_PROVIDER=ollama')
            content = content.replace('LLM_PROVIDER=azure', 'LLM_PROVIDER=ollama')
        else:
            content = 'LLM_PROVIDER=ollama\n' + content
        
        # Write back
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ Updated .env file for Ollama")
    else:
        # Copy from example and set Ollama
        if Path('.env.example').exists():
            with open('.env.example', 'r') as f:
                content = f.read()
            
            # Set Ollama as provider
            content = content.replace('LLM_PROVIDER=ollama', 'LLM_PROVIDER=ollama')
            
            with open('.env', 'w') as f:
                f.write(content)
            
            print("✅ Created .env file with Ollama configuration")
        else:
            print("❌ .env.example not found")

def test_ollama_model(model_name):
    """Test if a model works with a simple prompt."""
    print(f"🧪 Testing model: {model_name}")
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": model_name,
                "prompt": "Hello! Please respond with 'Model is working correctly.'",
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '').strip()
            print(f"✅ Model response: {response_text}")
            return True
        else:
            print(f"❌ Model test failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    """Main setup function."""
    print("🦙 Ollama Setup for AutoGen + ClickHouse Demo")
    print("=" * 50)
    
    # Check if Ollama is installed
    if not check_ollama_installed():
        print("\n📥 Please install Ollama first:")
        print("   Visit: https://ollama.ai/download")
        print("   Or use: curl -fsSL https://ollama.ai/install.sh | sh")
        return 1
    
    # Check if server is running, start if needed
    if not check_ollama_running():
        if not start_ollama_server():
            print("\n❌ Could not start Ollama server")
            print("Please start it manually: ollama serve")
            return 1
    
    # List available models
    available_models = list_available_models()
    print(f"\n📦 Available models: {available_models}")
    
    # Show recommendations
    recommend_models()
    
    # Suggest a model to pull if none available
    if not available_models:
        print("\n🎯 Recommended starter model: llama2:7b")
        choice = input("Would you like to pull llama2:7b? (y/n): ").strip().lower()
        
        if choice == 'y':
            if pull_model("llama2:7b"):
                test_ollama_model("llama2:7b")
        else:
            print("You can pull a model later with: ollama pull <model_name>")
    else:
        # Test the first available model
        test_model = available_models[0]
        test_ollama_model(test_model)
    
    # Set up environment
    setup_ollama_env()
    
    print("\n" + "=" * 50)
    print("🎉 Ollama setup complete!")
    print("\nNext steps:")
    print("1. Make sure your .env file has LLM_PROVIDER=ollama")
    print("2. Set OLLAMA_MODEL to your preferred model")
    print("3. Run the demo: python main.py")
    print("\nUseful Ollama commands:")
    print("  ollama list          - List installed models")
    print("  ollama pull <model>  - Download a model")
    print("  ollama run <model>   - Chat with a model")
    print("  ollama serve         - Start the server")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
