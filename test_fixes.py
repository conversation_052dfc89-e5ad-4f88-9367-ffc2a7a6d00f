#!/usr/bin/env python3
"""
Test script to verify the fixes for multiple query execution and conversation context.
"""

import requests
import json
import time

def test_multiple_query_execution():
    """Test that the AI agent executes multiple SQL queries for comprehensive questions."""
    
    print("🧪 Testing Multiple Query Execution")
    print("=" * 50)
    
    url = "http://localhost:8000/api/query"
    
    # Test question that should require multiple queries
    payload = {
        "question": "Show me sales data by region AND product categories - I need both analyses",
        "include_raw_data": False,
        "session_id": "test_multi_query"
    }
    
    print(f"Question: {payload['question']}")
    print("Sending request...")
    
    try:
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            answer = result['answer'].lower()
            
            print(f"\n✅ Response received (execution time: {result['execution_time']:.2f}s)")
            print(f"Answer length: {len(result['answer'])} characters")
            
            # Check if the response contains evidence of multiple queries
            indicators = [
                'region' in answer and 'category' in answer,  # Both data sets mentioned
                'query returned' in answer,  # Evidence of query execution
                len(answer) > 500,  # Substantial response suggesting multiple analyses
                any(word in answer for word in ['first', 'second', 'also', 'additionally', 'furthermore'])
            ]
            
            if sum(indicators) >= 2:
                print("✅ PASS: Response appears to contain multiple data analyses")
                print(f"Answer preview: {result['answer'][:300]}...")
                return True
            else:
                print("⚠️  UNCERTAIN: Response may not contain comprehensive multi-query analysis")
                print(f"Answer preview: {result['answer'][:300]}...")
                return False
        else:
            print(f"❌ Request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_conversation_context():
    """Test that the AI agent maintains conversation context between requests."""
    
    print("\n🧪 Testing Conversation Context")
    print("=" * 50)
    
    url = "http://localhost:8000/api/query"
    session_id = "test_context_session"
    
    # First question
    first_question = {
        "question": "What are our top 3 selling regions?",
        "include_raw_data": False,
        "session_id": session_id
    }
    
    print(f"First question: {first_question['question']}")
    
    try:
        # Send first question
        response1 = requests.post(url, json=first_question, timeout=60)
        
        if response1.status_code != 200:
            print(f"❌ First request failed: {response1.status_code}")
            return False
            
        result1 = response1.json()
        print(f"✅ First response received (execution time: {result1['execution_time']:.2f}s)")
        print(f"First answer preview: {result1['answer'][:200]}...")
        
        # Wait a moment
        time.sleep(2)
        
        # Follow-up question that references the previous response
        followup_question = {
            "question": "Show me the products sold in those top regions",
            "include_raw_data": False,
            "session_id": session_id
        }
        
        print(f"\nFollow-up question: {followup_question['question']}")
        
        # Send follow-up question
        response2 = requests.post(url, json=followup_question, timeout=60)
        
        if response2.status_code != 200:
            print(f"❌ Follow-up request failed: {response2.status_code}")
            return False
            
        result2 = response2.json()
        print(f"✅ Follow-up response received (execution time: {result2['execution_time']:.2f}s)")
        
        # Check if the follow-up response references the previous context
        answer2 = result2['answer'].lower()
        context_indicators = [
            'those' in answer2 or 'these' in answer2,  # Reference to previous results
            'top' in answer2 and 'region' in answer2,  # Reference to top regions
            'previous' in answer2 or 'mentioned' in answer2,  # Explicit context reference
            len(answer2) > 200,  # Substantial response suggesting it understood context
            'east' in answer2 and 'central' in answer2 and 'north' in answer2,  # Shows it knows the specific regions
            'product' in answer2 and 'region' in answer2  # Shows it's connecting products to regions
        ]
        
        if sum(context_indicators) >= 3:
            print("✅ PASS: Follow-up response shows conversation context awareness")
            print(f"Follow-up answer preview: {result2['answer'][:300]}...")
            return True
        else:
            print("⚠️  UNCERTAIN: Follow-up response may not show context awareness")
            print(f"Follow-up answer preview: {result2['answer'][:300]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_session_isolation():
    """Test that different sessions maintain separate conversation contexts."""
    
    print("\n🧪 Testing Session Isolation")
    print("=" * 50)
    
    url = "http://localhost:8000/api/query"
    
    # Session 1
    session1_question = {
        "question": "What are our sales in the North region?",
        "include_raw_data": False,
        "session_id": "session_1"
    }
    
    # Session 2  
    session2_question = {
        "question": "What are our sales in the South region?",
        "include_raw_data": False,
        "session_id": "session_2"
    }
    
    try:
        # Send questions to different sessions
        response1 = requests.post(url, json=session1_question, timeout=60)
        response2 = requests.post(url, json=session2_question, timeout=60)
        
        if response1.status_code == 200 and response2.status_code == 200:
            print("✅ PASS: Both sessions handled independently")
            return True
        else:
            print("❌ FAIL: Session isolation test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing AI Agent Fixes")
    print("Testing multiple query execution and conversation context")
    print("=" * 70)
    
    # Run tests
    test1_result = test_multiple_query_execution()
    test2_result = test_conversation_context()
    test3_result = test_session_isolation()
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"Multiple Query Execution: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Conversation Context: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"Session Isolation: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 ALL TESTS PASSED! Both fixes are working correctly.")
    else:
        print("\n⚠️  Some tests failed or were uncertain. Please review the results above.")
