#!/usr/bin/env python3
"""
测试AI SQL生成功能
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_service import create_ai_analyst, get_database_info, extract_sql_from_response

async def test_ai_sql_generation():
    """测试AI SQL生成功能"""
    print("🧪 开始测试AI SQL生成...")
    
    try:
        # 获取数据库信息
        print("📊 获取数据库信息...")
        db_info = get_database_info()
        if "error" in db_info:
            print(f"❌ 数据库信息获取失败: {db_info['error']}")
            return
        print(f"✅ 数据库信息获取成功，包含 {len(db_info.get('tables', []))} 个表")
        print(f"📋 表列表: {', '.join(db_info.get('tables', []))}")
        
        # 创建AI分析师
        print("🤖 创建AI分析师...")
        session_id = "test_session"
        analyst = await create_ai_analyst(db_info, session_id)
        print("✅ AI分析师创建成功")
        
        # 测试问题列表
        test_questions = [
            "What tables do we have?",
            "products 的类型有多少个？",
            "Analyze customer demographics",
            "Show sales performance by region",
            "How many customers do we have?"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 测试问题 {i}: {question}")
            
            try:
                # 生成SQL的提示
                sql_prompt = f"""
                Analyze this question and generate the appropriate SQL query: "{question}"
                
                Instructions:
                1. Look at the available database tables and their fields
                2. Generate a SQL query that answers the question
                3. Use exact field names from the database schema
                4. Respond with ONLY the SQL query, no explanations
                
                Question: {question}
                
                SQL Query:
                """
                
                print("🤖 向AI发送请求...")
                response = await analyst.run(task=sql_prompt)
                ai_response = response.messages[-1].content
                
                print(f"📝 AI原始响应: {ai_response[:200]}...")
                
                # 提取SQL
                sql_query = extract_sql_from_response(ai_response)
                print(f"🔧 提取的SQL: {sql_query}")
                
                # 验证SQL是否合理
                if sql_query and sql_query != "SHOW TABLES":
                    print("✅ SQL生成成功，不是默认的SHOW TABLES")
                elif sql_query == "SHOW TABLES" and "table" in question.lower():
                    print("✅ SQL生成正确，符合表查询需求")
                else:
                    print("⚠️ SQL可能不够智能，需要改进")
                
            except Exception as e:
                print(f"❌ 问题 {i} 测试失败: {e}")
                
        print("\n🎉 AI SQL生成测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_sql_generation())
