<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complex Task Display Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">Complex Task Display Test</h1>
        
        <div class="mb-6">
            <button onclick="testComplexTask()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Test Complex Task Display
            </button>
            <button onclick="testSimpleTask()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 ml-2">
                Test Simple Task Display
            </button>
            <button onclick="clearResults()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 ml-2">
                Clear Results
            </button>
        </div>

        <!-- Results Container -->
        <div id="results-container" style="display: none;">
            <div id="results-list"></div>
        </div>

        <!-- No Results -->
        <div id="no-results" style="display: block;">
            <div class="text-center py-8">
                <div class="text-gray-500">Click a button above to test the display</div>
            </div>
        </div>
    </div>

    <script src="js/utils-new.js"></script>
    <script>
        function testComplexTask() {
            const complexResult = {
                question: "分析日志数据中各个域名的访问情况以及不同HTTP方法的使用统计",
                task_decomposition_used: true,
                sub_tasks: [
                    "从日志数据中提取所有不同的域名",
                    "统计每个域名的访问次数",
                    "分析每个域名下使用的HTTP方法种类",
                    "统计每种HTTP方法在每个域名下的使用次数",
                    "汇总并展示每个域名的访问次数以及对应的HTTP方法分布情况"
                ],
                answer: `**复杂任务分析** (拆解为 5 个子任务):

**子任务 1**: 从日志数据中提取所有不同的域名

🔍 **执行的SQL查询:**
\`\`\`sql
SELECT DISTINCT host FROM logs
\`\`\`
📊 **查询结果:** 共 25 行数据，显示前 20 行:
host
ch.misterym.top
*************
bypaas.yuanbao.tencent.com
b2j3.com
tty.misterym.top
... 还有 20 行数据。

==================================================

**子任务 2**: 统计每个域名的访问次数

🔍 **执行的SQL查询:**
\`\`\`sql
SELECT host, COUNT(*) AS visit_count FROM logs GROUP BY host ORDER BY visit_count DESC
\`\`\`
📊 **查询结果:** 共 25 行数据，显示前 20 行:
host                        visit_count
bypaas.yuanbao.tencent.com  7988
gateway.misterym.top        4692
apibenchtest.misterym.top   4677
                            2690
*************               2655
... 还有 20 行数据。

==================================================

**子任务 3**: 分析每个域名下使用的HTTP方法种类

🔍 **执行的SQL查询:**
\`\`\`sql
SELECT host, method, COUNT(*) AS method_count FROM logs GROUP BY host, method ORDER BY host, method
\`\`\`
📊 **查询结果:** 共 33 行数据，显示前 20 行:
host                        method  method_count
                            GET     2616
                            POST    65
*************               GET     2657
... 还有 30 行数据。

==================================================

**综合分析总结**:

本次分析共执行了 5 个主要分析任务。每个任务都提供了具体的数据查询和分析结果，为您的决策提供了全面的数据支持。`,
                execution_time: 15.6,
                timestamp: new Date().toISOString()
            };

            displayResultsNew([complexResult]);
        }

        function testSimpleTask() {
            const simpleResult = {
                question: "显示数据库中的所有表",
                task_decomposition_used: false,
                answer: "这里是数据库中的所有表列表。查询执行成功，返回了系统中的所有表信息。",
                data: [
                    { table_name: "logs", rows: 29095 },
                    { table_name: "users", rows: 1250 },
                    { table_name: "sessions", rows: 3420 }
                ],
                execution_time: 2.3,
                timestamp: new Date().toISOString()
            };

            displayResultsNew([simpleResult]);
        }

        function clearResults() {
            document.getElementById('results-container').style.display = 'none';
            document.getElementById('no-results').style.display = 'block';
            document.getElementById('results-list').innerHTML = '';
        }
    </script>
</body>
</html>
